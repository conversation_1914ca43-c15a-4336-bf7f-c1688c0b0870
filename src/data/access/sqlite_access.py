"""
SQLite数据访问实现
"""
import sqlite3
import logging
from typing import List, Dict, Optional
from datetime import datetime
import os

from ...core.interfaces.data_access import IDataAccess
from ...core.exceptions.custom_exceptions import DataAccessError


class SQLiteDataAccess(IDataAccess):
    """SQLite数据访问实现"""

    def __init__(self, db_path: str = "data/stock_selection.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

        # 确保数据目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 初始化数据库
        self.init_database()

    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            return conn
        except Exception as e:
            raise DataAccessError(f"数据库连接失败: {str(e)}")

    def init_database(self) -> bool:
        """初始化数据库"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # 创建股票基本信息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS stock_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code VARCHAR(10) NOT NULL UNIQUE,
                        stock_name VARCHAR(50) NOT NULL,
                        industry VARCHAR(50),
                        market VARCHAR(10),
                        list_date DATE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建日交易数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_trading (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code VARCHAR(10) NOT NULL,
                        trade_date DATE NOT NULL,
                        open_price DECIMAL(10,2),
                        close_price DECIMAL(10,2),
                        high_price DECIMAL(10,2),
                        low_price DECIMAL(10,2),
                        volume BIGINT,
                        amount DECIMAL(15,2),
                        turnover_rate DECIMAL(8,4),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_code, trade_date)
                    )
                ''')

                # 创建选股结果表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS selection_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        strategy_name VARCHAR(50) NOT NULL,
                        stock_code VARCHAR(10) NOT NULL,
                        selection_date DATE NOT NULL,
                        score DECIMAL(8,4),
                        reason TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建系统配置表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_config (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        config_key VARCHAR(100) NOT NULL UNIQUE,
                        config_value TEXT,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_daily_trading_stock_date ON daily_trading(stock_code, trade_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_selection_results_strategy_date ON selection_results(strategy_name, selection_date)')

                conn.commit()
                self.logger.info("数据库初始化成功")
                return True

        except Exception as e:
            error_msg = f"数据库初始化失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def save_stock_info(self, stock_info: Dict) -> bool:
        """保存股票基本信息"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO stock_info
                    (stock_code, stock_name, industry, market, list_date, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    stock_info['stock_code'],
                    stock_info['stock_name'],
                    stock_info.get('industry', ''),
                    stock_info.get('market', ''),
                    stock_info.get('list_date'),
                    datetime.now()
                ))

                conn.commit()
                return True

        except Exception as e:
            error_msg = f"保存股票信息失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def save_stock_info_batch(self, stock_info_list: List[Dict]) -> bool:
        """批量保存股票基本信息"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                data_list = []
                for stock_info in stock_info_list:
                    # 处理上市日期
                    list_date = stock_info.get('list_date')
                    if list_date and hasattr(list_date, 'date'):
                        list_date = list_date.date()
                    elif isinstance(list_date, str):
                        try:
                            list_date = datetime.strptime(list_date, '%Y-%m-%d').date()
                        except:
                            list_date = None

                    data_list.append((
                        stock_info['stock_code'],
                        stock_info['stock_name'],
                        stock_info.get('industry', ''),
                        stock_info.get('market', ''),
                        list_date,
                        datetime.now()
                    ))

                cursor.executemany('''
                    INSERT OR REPLACE INTO stock_info
                    (stock_code, stock_name, industry, market, list_date, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', data_list)

                conn.commit()
                self.logger.info(f"批量保存股票信息成功，共 {len(stock_info_list)} 条记录")
                return True

        except Exception as e:
            error_msg = f"批量保存股票信息失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def save_daily_data(self, daily_data: List[Dict]) -> bool:
        """保存日交易数据"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                data_list = []
                for data in daily_data:
                    # 确保日期格式正确
                    trade_date = data['trade_date']
                    if hasattr(trade_date, 'date'):
                        trade_date = trade_date.date()
                    elif isinstance(trade_date, str):
                        trade_date = datetime.strptime(trade_date, '%Y-%m-%d').date()

                    data_list.append((
                        data['stock_code'],
                        trade_date,
                        data['open_price'],
                        data['close_price'],
                        data['high_price'],
                        data['low_price'],
                        data['volume'],
                        data['amount'],
                        data.get('turnover_rate'),
                        datetime.now()
                    ))

                cursor.executemany('''
                    INSERT OR REPLACE INTO daily_trading
                    (stock_code, trade_date, open_price, close_price, high_price,
                     low_price, volume, amount, turnover_rate, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', data_list)

                conn.commit()
                self.logger.info(f"保存日交易数据成功，共 {len(daily_data)} 条记录")
                return True

        except Exception as e:
            error_msg = f"保存日交易数据失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_stock_info(self, stock_code: str) -> Optional[Dict]:
        """获取股票基本信息"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM stock_info WHERE stock_code = ?
                ''', (stock_code,))

                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None

        except Exception as e:
            error_msg = f"获取股票信息失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_stock_data(self, stock_code: str,
                      start_date: datetime,
                      end_date: datetime) -> List[Dict]:
        """查询股票交易数据"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM daily_trading
                    WHERE stock_code = ? AND trade_date BETWEEN ? AND ?
                    ORDER BY trade_date
                ''', (stock_code, start_date.date(), end_date.date()))

                rows = cursor.fetchall()
                return [dict(row) for row in rows]

        except Exception as e:
            error_msg = f"查询股票交易数据失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_all_stock_codes(self) -> List[str]:
        """获取所有股票代码"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('SELECT stock_code FROM stock_info ORDER BY stock_code')
                rows = cursor.fetchall()
                return [row['stock_code'] for row in rows]

        except Exception as e:
            error_msg = f"获取股票代码列表失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_latest_trade_date(self, stock_code: str) -> Optional[datetime]:
        """获取股票最新交易日期"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT MAX(trade_date) as latest_date
                    FROM daily_trading
                    WHERE stock_code = ?
                ''', (stock_code,))

                row = cursor.fetchone()
                if row and row['latest_date']:
                    return datetime.strptime(row['latest_date'], '%Y-%m-%d')
                return None

        except Exception as e:
            error_msg = f"获取最新交易日期失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def save_selection_result(self, selection_result: Dict) -> bool:
        """保存选股结果"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO selection_results
                    (strategy_name, stock_code, selection_date, score, reason)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    selection_result['strategy_name'],
                    selection_result['stock_code'],
                    selection_result['selection_date'],
                    selection_result['score'],
                    selection_result['reason']
                ))

                conn.commit()
                return True

        except Exception as e:
            error_msg = f"保存选股结果失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def get_selection_results(self, strategy_name: str,
                            selection_date: datetime) -> List[Dict]:
        """获取选股结果"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT sr.*, si.stock_name
                    FROM selection_results sr
                    LEFT JOIN stock_info si ON sr.stock_code = si.stock_code
                    WHERE sr.strategy_name = ? AND sr.selection_date = ?
                    ORDER BY sr.score DESC
                ''', (strategy_name, selection_date.date()))

                rows = cursor.fetchall()
                return [dict(row) for row in rows]

        except Exception as e:
            error_msg = f"获取选股结果失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataAccessError(error_msg)

    def close_connection(self) -> None:
        """关闭数据库连接"""
        # SQLite连接是自动管理的，这里不需要特殊处理
        pass
