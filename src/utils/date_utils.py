"""
日期工具模块
"""
from datetime import datetime, timedelta
from typing import List


def get_trading_days(start_date: datetime, end_date: datetime) -> List[datetime]:
    """
    获取交易日列表（简单实现，排除周末）
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        List[datetime]: 交易日列表
    """
    trading_days = []
    current_date = start_date
    
    while current_date <= end_date:
        # 排除周末（周六=5，周日=6）
        if current_date.weekday() < 5:
            trading_days.append(current_date)
        current_date += timedelta(days=1)
    
    return trading_days


def get_last_n_trading_days(n: int, end_date: datetime = None) -> List[datetime]:
    """
    获取最近N个交易日
    
    Args:
        n: 天数
        end_date: 结束日期，默认为今天
        
    Returns:
        List[datetime]: 交易日列表
    """
    if end_date is None:
        end_date = datetime.now()
    
    # 向前推算足够的天数以确保能找到N个交易日
    start_date = end_date - timedelta(days=n * 2)
    
    trading_days = get_trading_days(start_date, end_date)
    
    # 返回最近的N个交易日
    return trading_days[-n:] if len(trading_days) >= n else trading_days


def format_date(date: datetime, format_str: str = "%Y-%m-%d") -> str:
    """
    格式化日期
    
    Args:
        date: 日期对象
        format_str: 格式字符串
        
    Returns:
        str: 格式化后的日期字符串
    """
    return date.strftime(format_str)


def parse_date(date_str: str, format_str: str = "%Y-%m-%d") -> datetime:
    """
    解析日期字符串
    
    Args:
        date_str: 日期字符串
        format_str: 格式字符串
        
    Returns:
        datetime: 日期对象
    """
    return datetime.strptime(date_str, format_str)
