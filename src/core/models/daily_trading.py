"""
日交易数据模型
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class DailyTrading:
    """日交易数据"""
    stock_code: str
    trade_date: datetime
    open_price: float
    close_price: float
    high_price: float
    low_price: float
    volume: int
    amount: float
    turnover_rate: Optional[float] = None
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'stock_code': self.stock_code,
            'trade_date': self.trade_date,
            'open_price': self.open_price,
            'close_price': self.close_price,
            'high_price': self.high_price,
            'low_price': self.low_price,
            'volume': self.volume,
            'amount': self.amount,
            'turnover_rate': self.turnover_rate,
            'created_at': self.created_at
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'DailyTrading':
        """从字典创建对象"""
        return cls(
            stock_code=data['stock_code'],
            trade_date=data['trade_date'],
            open_price=float(data['open_price']),
            close_price=float(data['close_price']),
            high_price=float(data['high_price']),
            low_price=float(data['low_price']),
            volume=int(data['volume']),
            amount=float(data['amount']),
            turnover_rate=float(data['turnover_rate']) if data.get('turnover_rate') else None,
            created_at=data.get('created_at')
        )
    
    def get_price_change(self) -> float:
        """计算涨跌幅"""
        if self.open_price == 0:
            return 0.0
        return (self.close_price - self.open_price) / self.open_price * 100
    
    def __str__(self) -> str:
        return f"{self.stock_code} {self.trade_date.strftime('%Y-%m-%d')} 收盘:{self.close_price} 成交量:{self.volume}"
