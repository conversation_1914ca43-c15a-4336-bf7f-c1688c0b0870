"""
股票基本信息数据模型
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class StockInfo:
    """股票基本信息"""
    stock_code: str
    stock_name: str
    industry: str
    market: str
    list_date: datetime
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'stock_code': self.stock_code,
            'stock_name': self.stock_name,
            'industry': self.industry,
            'market': self.market,
            'list_date': self.list_date,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'StockInfo':
        """从字典创建对象"""
        return cls(
            stock_code=data['stock_code'],
            stock_name=data['stock_name'],
            industry=data.get('industry', ''),
            market=data.get('market', ''),
            list_date=data['list_date'],
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )
    
    def __str__(self) -> str:
        return f"{self.stock_code} - {self.stock_name} ({self.industry})"
