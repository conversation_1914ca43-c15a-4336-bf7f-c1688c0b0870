"""
选股结果数据模型
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class SelectionResult:
    """选股结果"""
    strategy_name: str
    stock_code: str
    stock_name: str
    selection_date: datetime
    score: float
    reason: str
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'strategy_name': self.strategy_name,
            'stock_code': self.stock_code,
            'stock_name': self.stock_name,
            'selection_date': self.selection_date,
            'score': self.score,
            'reason': self.reason,
            'created_at': self.created_at
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'SelectionResult':
        """从字典创建对象"""
        return cls(
            strategy_name=data['strategy_name'],
            stock_code=data['stock_code'],
            stock_name=data['stock_name'],
            selection_date=data['selection_date'],
            score=float(data['score']),
            reason=data['reason'],
            created_at=data.get('created_at')
        )
    
    def __str__(self) -> str:
        return f"{self.stock_code} - {self.stock_name} (评分:{self.score:.2f}) - {self.reason}"
