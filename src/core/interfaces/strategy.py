"""
选股策略抽象接口定义
"""
from abc import ABC, abstractmethod
from typing import List, Dict
from .data_access import IDataAccess


class ISelectionStrategy(ABC):
    """选股策略抽象接口"""
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """
        获取策略名称
        
        Returns:
            str: 策略名称
        """
        pass
    
    @abstractmethod
    def get_strategy_description(self) -> str:
        """
        获取策略描述
        
        Returns:
            str: 策略描述
        """
        pass
    
    @abstractmethod
    def execute(self, data_access: IDataAccess) -> List[Dict]:
        """
        执行选股策略
        
        Args:
            data_access: 数据访问接口
            
        Returns:
            List[Dict]: 选股结果列表，每个字典包含：
                - stock_code: 股票代码
                - stock_name: 股票名称
                - score: 评分
                - reason: 选中原因
                - selection_date: 选股日期
        """
        pass
    
    @abstractmethod
    def get_config(self) -> Dict:
        """
        获取策略配置
        
        Returns:
            Dict: 策略配置参数
        """
        pass
    
    @abstractmethod
    def set_config(self, config: Dict) -> None:
        """
        设置策略配置
        
        Args:
            config: 策略配置参数
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict) -> bool:
        """
        验证策略配置
        
        Args:
            config: 策略配置参数
            
        Returns:
            bool: 配置是否有效
        """
        pass
