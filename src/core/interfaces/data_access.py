"""
数据访问抽象接口定义
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from datetime import datetime


class IDataAccess(ABC):
    """数据访问抽象接口"""
    
    @abstractmethod
    def save_stock_info(self, stock_info: Dict) -> bool:
        """
        保存股票基本信息
        
        Args:
            stock_info: 股票信息字典
            
        Returns:
            bool: 保存是否成功
        """
        pass
    
    @abstractmethod
    def save_stock_info_batch(self, stock_info_list: List[Dict]) -> bool:
        """
        批量保存股票基本信息
        
        Args:
            stock_info_list: 股票信息列表
            
        Returns:
            bool: 保存是否成功
        """
        pass
    
    @abstractmethod
    def save_daily_data(self, daily_data: List[Dict]) -> bool:
        """
        保存日交易数据
        
        Args:
            daily_data: 日交易数据列表
            
        Returns:
            bool: 保存是否成功
        """
        pass
    
    @abstractmethod
    def get_stock_info(self, stock_code: str) -> Optional[Dict]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Optional[Dict]: 股票信息，如果不存在返回None
        """
        pass
    
    @abstractmethod
    def get_stock_data(self, stock_code: str, 
                      start_date: datetime, 
                      end_date: datetime) -> List[Dict]:
        """
        查询股票交易数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict]: 交易数据列表
        """
        pass
    
    @abstractmethod
    def get_all_stock_codes(self) -> List[str]:
        """
        获取所有股票代码
        
        Returns:
            List[str]: 股票代码列表
        """
        pass
    
    @abstractmethod
    def get_latest_trade_date(self, stock_code: str) -> Optional[datetime]:
        """
        获取股票最新交易日期
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Optional[datetime]: 最新交易日期，如果没有数据返回None
        """
        pass
    
    @abstractmethod
    def save_selection_result(self, selection_result: Dict) -> bool:
        """
        保存选股结果
        
        Args:
            selection_result: 选股结果字典
            
        Returns:
            bool: 保存是否成功
        """
        pass
    
    @abstractmethod
    def get_selection_results(self, strategy_name: str, 
                            selection_date: datetime) -> List[Dict]:
        """
        获取选股结果
        
        Args:
            strategy_name: 策略名称
            selection_date: 选股日期
            
        Returns:
            List[Dict]: 选股结果列表
        """
        pass
    
    @abstractmethod
    def init_database(self) -> bool:
        """
        初始化数据库
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def close_connection(self) -> None:
        """
        关闭数据库连接
        """
        pass
