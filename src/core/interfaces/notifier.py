"""
通知抽象接口定义
"""
from abc import ABC, abstractmethod
from typing import Dict, List


class INotifier(ABC):
    """通知抽象接口"""
    
    @abstractmethod
    def send_notification(self, title: str, content: str, **kwargs) -> bool:
        """
        发送通知
        
        Args:
            title: 通知标题
            content: 通知内容
            **kwargs: 其他参数
            
        Returns:
            bool: 发送是否成功
        """
        pass
    
    @abstractmethod
    def send_selection_results(self, results: List[Dict], 
                             strategy_name: str, 
                             execution_stats: Dict) -> bool:
        """
        发送选股结果通知
        
        Args:
            results: 选股结果列表
            strategy_name: 策略名称
            execution_stats: 执行统计信息
            
        Returns:
            bool: 发送是否成功
        """
        pass
    
    @abstractmethod
    def get_notifier_name(self) -> str:
        """
        获取通知器名称
        
        Returns:
            str: 通知器名称
        """
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """
        测试连接
        
        Returns:
            bool: 连接是否正常
        """
        pass
