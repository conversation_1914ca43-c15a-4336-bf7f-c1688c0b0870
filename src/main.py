#!/usr/bin/env python3
"""
A股智能选股系统主程序
v0.1版本：基础框架和数据获取功能
"""
import sys
import os
import argparse
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.sources.akshare_source import AkshareDataSource
from src.data.access.sqlite_access import SQLiteDataAccess
from src.utils.logger import setup_logger


def init_system():
    """初始化系统"""
    # 设置日志
    logger = setup_logger(
        name="stock_selection",
        log_level="INFO",
        log_file="logs/app.log"
    )
    
    # 确保必要目录存在
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data", exist_ok=True)
    
    logger.info("系统初始化完成")
    return logger


def update_stock_list(data_source: AkshareDataSource, 
                     data_access: SQLiteDataAccess, 
                     logger) -> bool:
    """更新股票列表"""
    try:
        logger.info("开始更新股票列表...")
        
        # 获取股票列表
        stock_list = data_source.get_stock_list()
        
        # 保存到数据库
        success = data_access.save_stock_info_batch(stock_list)
        
        if success:
            logger.info(f"股票列表更新成功，共 {len(stock_list)} 只股票")
            return True
        else:
            logger.error("股票列表保存失败")
            return False
            
    except Exception as e:
        logger.error(f"更新股票列表失败: {str(e)}")
        return False


def update_daily_data(data_source: AkshareDataSource, 
                     data_access: SQLiteDataAccess, 
                     logger,
                     days: int = 30,
                     limit: int = None) -> bool:
    """更新日交易数据"""
    try:
        logger.info(f"开始更新最近 {days} 天的交易数据...")
        
        # 获取所有股票代码
        stock_codes = data_access.get_all_stock_codes()
        
        if not stock_codes:
            logger.warning("数据库中没有股票信息，请先更新股票列表")
            return False
        
        # 限制处理数量（用于测试）
        if limit:
            stock_codes = stock_codes[:limit]
            logger.info(f"限制处理股票数量: {limit}")
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        success_count = 0
        total_count = len(stock_codes)
        
        for i, stock_code in enumerate(stock_codes, 1):
            try:
                logger.info(f"处理股票 {stock_code} ({i}/{total_count})")
                
                # 获取日K线数据
                daily_data = data_source.get_daily_data(stock_code, start_date, end_date)
                
                if daily_data:
                    # 保存数据
                    data_access.save_daily_data(daily_data)
                    success_count += 1
                    logger.info(f"  成功保存 {len(daily_data)} 条记录")
                else:
                    logger.warning(f"  股票 {stock_code} 无数据")
                
            except Exception as e:
                logger.error(f"  处理股票 {stock_code} 失败: {str(e)}")
                continue
        
        logger.info(f"交易数据更新完成，成功处理 {success_count}/{total_count} 只股票")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"更新交易数据失败: {str(e)}")
        return False


def show_statistics(data_access: SQLiteDataAccess, logger):
    """显示统计信息"""
    try:
        logger.info("系统统计信息:")
        
        # 股票数量
        stock_codes = data_access.get_all_stock_codes()
        logger.info(f"  股票总数: {len(stock_codes)}")
        
        # 随机检查几只股票的数据情况
        if stock_codes:
            sample_codes = stock_codes[:5]
            logger.info("  样本股票数据情况:")
            
            for stock_code in sample_codes:
                latest_date = data_access.get_latest_trade_date(stock_code)
                stock_info = data_access.get_stock_info(stock_code)
                
                if stock_info and latest_date:
                    logger.info(f"    {stock_code} - {stock_info['stock_name']}: "
                              f"最新数据 {latest_date.strftime('%Y-%m-%d')}")
                else:
                    logger.info(f"    {stock_code}: 无交易数据")
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="A股智能选股系统 v0.1")
    parser.add_argument("--update-stocks", action="store_true", 
                       help="更新股票列表")
    parser.add_argument("--update-data", action="store_true", 
                       help="更新交易数据")
    parser.add_argument("--days", type=int, default=30, 
                       help="更新最近N天的数据（默认30天）")
    parser.add_argument("--limit", type=int, 
                       help="限制处理的股票数量（用于测试）")
    parser.add_argument("--stats", action="store_true", 
                       help="显示统计信息")
    
    args = parser.parse_args()
    
    # 初始化系统
    logger = init_system()
    logger.info("A股智能选股系统 v0.1 启动")
    
    try:
        # 创建数据源和数据访问实例
        data_source = AkshareDataSource()
        data_access = SQLiteDataAccess()
        
        logger.info(f"数据源: {data_source.get_data_source_name()}")
        
        # 根据参数执行相应操作
        if args.update_stocks:
            success = update_stock_list(data_source, data_access, logger)
            if not success:
                logger.error("股票列表更新失败")
                return False
        
        if args.update_data:
            success = update_daily_data(data_source, data_access, logger, 
                                      args.days, args.limit)
            if not success:
                logger.error("交易数据更新失败")
                return False
        
        if args.stats or (not args.update_stocks and not args.update_data):
            show_statistics(data_access, logger)
        
        logger.info("程序执行完成")
        return True
        
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        return False
    
    finally:
        # 清理资源
        if 'data_access' in locals():
            data_access.close_connection()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
