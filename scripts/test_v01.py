#!/usr/bin/env python3
"""
v0.1版本测试脚本
测试数据获取和存储功能
"""
import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.sources.akshare_source import AkshareDataSource
from src.data.access.sqlite_access import SQLiteDataAccess


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/test_v01.log')
        ]
    )


def test_data_source():
    """测试数据源功能"""
    print("=" * 50)
    print("测试数据源功能")
    print("=" * 50)
    
    try:
        # 创建数据源实例
        data_source = AkshareDataSource()
        print(f"数据源: {data_source.get_data_source_name()}")
        
        # 测试股票代码验证
        print("\n1. 测试股票代码验证:")
        test_codes = ["000001", "600000", "300001", "123456", "abc123"]
        for code in test_codes:
            is_valid = data_source.validate_stock_code(code)
            print(f"  {code}: {'有效' if is_valid else '无效'}")
        
        # 测试获取股票列表（限制数量以节省时间）
        print("\n2. 测试获取股票列表:")
        stock_list = data_source.get_stock_list()
        print(f"  获取到 {len(stock_list)} 只股票")
        
        # 显示前5只股票信息
        print("  前5只股票:")
        for i, stock in enumerate(stock_list[:5]):
            print(f"    {i+1}. {stock['stock_code']} - {stock['stock_name']} ({stock['market']})")
        
        # 测试获取日K线数据
        print("\n3. 测试获取日K线数据:")
        test_stock = "000001"  # 平安银行
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        daily_data = data_source.get_daily_data(test_stock, start_date, end_date)
        print(f"  获取股票 {test_stock} 最近30天数据: {len(daily_data)} 条记录")
        
        if daily_data:
            latest = daily_data[-1]
            print(f"  最新数据: {latest['trade_date'].strftime('%Y-%m-%d')} "
                  f"收盘价: {latest['close_price']} 成交量: {latest['volume']}")
        
        return stock_list[:10], daily_data  # 返回前10只股票和测试数据
        
    except Exception as e:
        print(f"数据源测试失败: {str(e)}")
        return [], []


def test_data_access(stock_list, daily_data):
    """测试数据访问功能"""
    print("\n" + "=" * 50)
    print("测试数据访问功能")
    print("=" * 50)
    
    try:
        # 创建数据访问实例
        data_access = SQLiteDataAccess()
        print("数据库初始化成功")
        
        # 测试保存股票信息
        print("\n1. 测试保存股票信息:")
        if stock_list:
            success = data_access.save_stock_info_batch(stock_list)
            print(f"  批量保存股票信息: {'成功' if success else '失败'}")
            
            # 验证保存结果
            saved_codes = data_access.get_all_stock_codes()
            print(f"  数据库中股票数量: {len(saved_codes)}")
        
        # 测试保存交易数据
        print("\n2. 测试保存交易数据:")
        if daily_data:
            success = data_access.save_daily_data(daily_data)
            print(f"  保存交易数据: {'成功' if success else '失败'}")
            
            # 验证保存结果
            test_stock = daily_data[0]['stock_code']
            latest_date = data_access.get_latest_trade_date(test_stock)
            print(f"  股票 {test_stock} 最新交易日期: {latest_date}")
        
        # 测试查询功能
        print("\n3. 测试查询功能:")
        if stock_list:
            test_stock = stock_list[0]['stock_code']
            stock_info = data_access.get_stock_info(test_stock)
            if stock_info:
                print(f"  查询股票信息: {stock_info['stock_code']} - {stock_info['stock_name']}")
            
            # 查询交易数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            trading_data = data_access.get_stock_data(test_stock, start_date, end_date)
            print(f"  查询最近7天交易数据: {len(trading_data)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"数据访问测试失败: {str(e)}")
        return False


def test_integration():
    """集成测试"""
    print("\n" + "=" * 50)
    print("集成测试")
    print("=" * 50)
    
    try:
        # 创建实例
        data_source = AkshareDataSource()
        data_access = SQLiteDataAccess()
        
        print("1. 获取股票列表并保存到数据库...")
        stock_list = data_source.get_stock_list()
        
        # 只保存前20只股票以节省时间
        limited_stock_list = stock_list[:20]
        data_access.save_stock_info_batch(limited_stock_list)
        print(f"   保存了 {len(limited_stock_list)} 只股票信息")
        
        print("\n2. 获取部分股票的交易数据...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=10)
        
        success_count = 0
        for i, stock in enumerate(limited_stock_list[:5]):  # 只测试前5只
            try:
                stock_code = stock['stock_code']
                daily_data = data_source.get_daily_data(stock_code, start_date, end_date)
                
                if daily_data:
                    data_access.save_daily_data(daily_data)
                    success_count += 1
                    print(f"   {stock_code}: {len(daily_data)} 条记录")
                
            except Exception as e:
                print(f"   {stock_code}: 失败 - {str(e)}")
        
        print(f"\n3. 集成测试完成，成功处理 {success_count} 只股票")
        
        # 统计信息
        all_codes = data_access.get_all_stock_codes()
        print(f"   数据库中总股票数: {len(all_codes)}")
        
        return True
        
    except Exception as e:
        print(f"集成测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("A股智能选股系统 v0.1 测试")
    print("测试基础框架和数据获取功能")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    try:
        # 测试数据源
        stock_list, daily_data = test_data_source()
        
        # 测试数据访问
        data_access_success = test_data_access(stock_list, daily_data)
        
        # 集成测试
        integration_success = test_integration()
        
        # 总结
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"数据源测试: {'通过' if stock_list else '失败'}")
        print(f"数据访问测试: {'通过' if data_access_success else '失败'}")
        print(f"集成测试: {'通过' if integration_success else '失败'}")
        
        if stock_list and data_access_success and integration_success:
            print("\n🎉 v0.1版本测试全部通过！")
            print("✅ 基础框架搭建完成")
            print("✅ 数据获取功能正常")
            print("✅ 数据存储功能正常")
            print("✅ 系统集成测试通过")
            return True
        else:
            print("\n❌ 部分测试失败，请检查错误信息")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
