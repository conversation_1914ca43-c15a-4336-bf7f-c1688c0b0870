# A股智能选股系统 - 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景
开发一个自动化的A股选股系统，通过定时获取股票交易数据，运用特定的量化策略筛选出符合条件的投资标的，为投资决策提供数据支持。

### 1.2 项目目标
- 自动化获取A股市场全量交易数据
- 实现基于交易量异动的选股策略
- 提供可扩展的策略框架，支持多种选股算法
- 建立稳定的数据存储和查询系统

### 1.3 目标用户
- 个人投资者
- 量化交易爱好者
- 股票分析师

## 2. 功能需求

### 2.1 数据获取模块
**功能描述**：通过抽象接口设计，支持多数据源的A股交易数据获取

**架构设计**：
- **数据源抽象接口**：定义统一的数据获取接口规范
- **akshare实现**：基于akshare库的具体实现
- **数据源适配器**：支持后续扩展其他数据源（tushare、新浪财经等）

**获取数据类型**：
- 股票基本信息（代码、名称、行业、上市日期等）
- 日K线数据（开盘价、收盘价、最高价、最低价）
- 成交量、成交额数据
- 换手率、流通市值等

**技术要求**：
- 接口与实现分离，便于数据源切换
- 支持增量更新和全量更新
- 数据获取失败时的重试机制
- 数据质量校验和异常处理
- 支持并发获取以提高效率
- 统一的数据格式转换

### 2.2 数据存储模块
**功能描述**：基于数据访问层设计，实现灵活的数据存储方案

**架构设计**：
- **数据访问层抽象接口**：定义统一的数据操作接口（DAO层）
- **SQLite实现**：基于SQLite的具体实现
- **数据库适配器**：支持后续扩展其他数据库（MySQL、PostgreSQL等）

**数据表设计**：
- 股票基本信息表（stock_info）
- 日交易数据表（daily_trading）
- 选股结果表（selection_results）
- 系统配置表（system_config）

**技术要求**：
- 接口与实现分离，便于数据库切换
- 支持事务处理
- 数据索引优化
- 数据备份和恢复机制
- 连接池管理

### 2.3 选股策略模块
**功能描述**：基于插件化架构实现可扩展的选股策略系统

**架构设计**：
- **策略抽象接口**：定义统一的选股策略接口规范
- **策略依赖隔离**：每个策略独立运行，互不影响
- **策略工厂模式**：动态加载和管理不同策略
- **策略配置管理**：支持策略参数的灵活配置

**初始策略 - 交易量异动选股**：
- **策略描述**：筛选过去2周交易量较低，但今日交易量急剧放大的股票
- **具体指标**：
  - 过去2周平均交易量基准：低于过去3个月平均值的70%
  - 今日交易量放大倍数：超过过去2周平均值的3倍以上
  - 基础过滤条件：
    - 股价范围：5-100元（避免ST股和过高价格股）
    - 上市时间：超过1年
    - 非ST、*ST股票

**策略框架特性**：
- 策略插件化，便于扩展新策略
- 策略参数可配置化
- 策略独立性保证
- 支持策略组合运行

### 2.4 定时任务模块
**功能描述**：自动化执行数据获取和选股任务

**具体需求**：
- **定时频率**：每日收盘后16:00执行
- **任务类型**：
  - 数据获取任务：获取当日交易数据
  - 选股计算任务：运行选股策略
  - 结果通知任务：发送选股结果
- **任务调度管理**：基于APScheduler实现
- **任务执行监控**：记录任务执行状态和耗时
- **异常处理**：失败重试机制和错误告警

### 2.5 结果通知模块
**功能描述**：多渠道展示和通知选股结果

**通知方式**：
- **控制台输出**：实时显示选股结果和执行状态
- **邮件通知**：发送详细的选股报告到指定邮箱
- **日志记录**：完整记录系统运行和选股历史

**输出内容**：
- 选股结果列表（股票代码、名称、关键指标）
- 策略执行统计（命中数量、执行时间等）
- 系统运行状态（数据获取状态、异常信息等）

## 3. 非功能需求

### 3.1 性能要求
- 数据获取：支持全市场4000+股票数据获取
- 并发处理：支持多线程数据获取

### 3.2 可靠性要求
- 系统可用性：99%
- 数据准确性：99.9%
- 异常恢复：自动重试机制

### 3.3 可扩展性要求
- 支持新增数据源
- 支持新增选股策略
- 支持水平扩展

### 3.4 安全性要求
- 数据访问权限控制
- API调用频率限制
- 敏感信息加密存储

## 4. 技术架构

### 4.1 技术栈
- **编程语言**：Python 3.8+
- **数据库**：SQLite（支持后续扩展MySQL/PostgreSQL）
- **数据获取**：akshare（支持后续扩展其他数据源）
- **任务调度**：APScheduler
- **邮件发送**：smtplib + email
- **日志管理**：logging
- **配置管理**：configparser 或 YAML

### 4.2 系统架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        A股智能选股系统                          │
├─────────────────────────────────────────────────────────────────┤
│  应用层                                                         │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   定时任务模块   │    │   结果通知模块   │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│  业务层                                                         │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   选股策略模块   │    │   配置管理模块   │                    │
│  │  (策略接口)     │    │                 │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│  数据层                                                         │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   数据获取模块   │    │   数据存储模块   │                    │
│  │  (数据源接口)   │    │   (DAO接口)     │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│  基础设施层                                                     │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │  akshare实现    │    │   SQLite实现    │                    │
│  └─────────────────┘    └─────────────────┘                    │
└─────────────────────────────────────────────────────────────────┘
```

## 5. 项目计划

### 5.1 开发阶段
1. **第一阶段**：基础框架搭建（1周）
   - 项目结构设计
   - 数据库设计
   - 基础工具类开发

2. **第二阶段**：数据获取模块（1周）
   - 数据源接入
   - 数据获取逻辑
   - 数据存储逻辑

3. **第三阶段**：选股策略模块（1周）
   - 核心选股算法实现
   - 策略框架设计
   - 参数配置系统

4. **第四阶段**：系统集成和测试（1周）
   - 定时任务集成
   - 系统测试
   - 性能优化

### 5.2 里程碑
- [ ] 完成需求确认和技术选型
- [ ] 完成基础框架和数据库设计
- [ ] 完成数据获取功能
- [ ] 完成核心选股策略
- [ ] 完成系统集成测试
- [ ] 项目上线运行

## 6. 风险评估

### 6.1 技术风险
- 数据源稳定性风险
- 数据获取频率限制
- 网络连接异常

### 6.2 业务风险
- 选股策略有效性
- 市场环境变化影响
- 数据质量问题

## 7. 核心设计原则

### 7.1 接口抽象原则
- **数据源抽象**：通过接口隔离具体数据源实现，便于切换
- **存储抽象**：通过DAO层隔离具体数据库实现，便于扩展
- **策略抽象**：通过策略接口实现插件化，便于新增策略

### 7.2 依赖隔离原则
- **策略独立性**：每个选股策略独立运行，互不影响
- **模块解耦**：各模块通过接口通信，降低耦合度
- **配置外部化**：所有配置参数外部化管理

### 7.3 可扩展性原则
- **水平扩展**：支持多策略并行运行
- **垂直扩展**：支持新增数据源和存储方案
- **功能扩展**：支持新增通知方式和分析功能

## 8. 项目里程碑

### 8.1 已确认需求
- ✅ 数据源选择：akshare（接口抽象设计）
- ✅ 数据库选型：SQLite（DAO层抽象设计）
- ✅ 选股策略：交易量异动策略（策略接口设计）
- ✅ 运行频率：每日16:00定时执行
- ✅ 结果通知：控制台输出 + 邮件通知
- ✅ 架构设计：分层架构 + 接口抽象

### 8.2 下一步行动
1. 创建项目基础结构和虚拟环境
2. 实现核心接口定义
3. 开发数据获取模块（akshare实现）
4. 开发数据存储模块（SQLite实现）
5. 开发选股策略模块（交易量异动策略）
6. 集成定时任务和通知功能
7. 系统测试和优化

---

**文档版本**：v2.0
**创建日期**：2024年12月
**最后更新**：需求确认完成，准备开始开发
