Metadata-Version: 2.1
Name: parso
Version: 0.8.4
Summary: A Python Parser
Home-page: https://github.com/davidhalter/parso
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON>
Maintainer-email: <EMAIL>
License: MIT
Keywords: python parser parsing
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Plugins
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Text Editors :: Integrated Development Environments (IDE)
Classifier: Topic :: Utilities
Classifier: Typing :: Typed
Requires-Python: >=3.6
Provides-Extra: qa
Requires-Dist: flake8 (==5.0.4) ; extra == 'qa'
Requires-Dist: mypy (==0.971) ; extra == 'qa'
Requires-Dist: types-setuptools (==67.2.0.1) ; extra == 'qa'
Provides-Extra: testing
Requires-Dist: docopt ; extra == 'testing'
Requires-Dist: pytest ; extra == 'testing'

###################################################################
parso - A Python Parser
###################################################################


.. image:: https://github.com/davidhalter/parso/workflows/Build/badge.svg?branch=master
    :target: https://github.com/davidhalter/parso/actions
    :alt: GitHub Actions build status

.. image:: https://coveralls.io/repos/github/davidhalter/parso/badge.svg?branch=master
    :target: https://coveralls.io/github/davidhalter/parso?branch=master
    :alt: Coverage Status

.. image:: https://pepy.tech/badge/parso
    :target: https://pepy.tech/project/parso
    :alt: PyPI Downloads

.. image:: https://raw.githubusercontent.com/davidhalter/parso/master/docs/_static/logo_characters.png

Parso is a Python parser that supports error recovery and round-trip parsing
for different Python versions (in multiple Python versions). Parso is also able
to list multiple syntax errors in your python file.

Parso has been battle-tested by jedi_. It was pulled out of jedi to be useful
for other projects as well.

Parso consists of a small API to parse Python and analyse the syntax tree.

A simple example:

.. code-block:: python

    >>> import parso
    >>> module = parso.parse('hello + 1', version="3.9")
    >>> expr = module.children[0]
    >>> expr
    PythonNode(arith_expr, [<Name: hello@1,0>, <Operator: +>, <Number: 1>])
    >>> print(expr.get_code())
    hello + 1
    >>> name = expr.children[0]
    >>> name
    <Name: hello@1,0>
    >>> name.end_pos
    (1, 5)
    >>> expr.end_pos
    (1, 9)

To list multiple issues:

.. code-block:: python

    >>> grammar = parso.load_grammar()
    >>> module = grammar.parse('foo +\nbar\ncontinue')
    >>> error1, error2 = grammar.iter_errors(module)
    >>> error1.message
    'SyntaxError: invalid syntax'
    >>> error2.message
    "SyntaxError: 'continue' not properly in loop"

Resources
=========

- `Testing <https://parso.readthedocs.io/en/latest/docs/development.html#testing>`_
- `PyPI <https://pypi.python.org/pypi/parso>`_
- `Docs <https://parso.readthedocs.org/en/latest/>`_
- Uses `semantic versioning <https://semver.org/>`_

Installation
============

    pip install parso

Future
======

- There will be better support for refactoring and comments. Stay tuned.
- There's a WIP PEP8 validator. It's however not in a good shape, yet.

Known Issues
============

- `async`/`await` are already used as keywords in Python3.6.
- `from __future__ import print_function` is not ignored.


Acknowledgements
================

- Guido van Rossum (@gvanrossum) for creating the parser generator pgen2
  (originally used in lib2to3).
- `Salome Schneider <https://www.crepes-schnaegg.ch/cr%C3%AApes-schn%C3%A4gg/kunst-f%C3%BCrs-cr%C3%AApes-mobil/>`_
  for the extremely awesome parso logo.


.. _jedi: https://github.com/davidhalter/jedi


.. :changelog:

Changelog
---------

Unreleased
++++++++++

0.8.4 (2024-04-05)
++++++++++++++++++

- Add basic support for Python 3.13

0.8.3 (2021-11-30)
++++++++++++++++++

- Add basic support for Python 3.11 and 3.12

0.8.2 (2021-03-30)
++++++++++++++++++

- Various small bugfixes

0.8.1 (2020-12-10)
++++++++++++++++++

- Various small bugfixes

0.8.0 (2020-08-05)
++++++++++++++++++

- Dropped Support for Python 2.7, 3.4, 3.5
- It's possible to use ``pathlib.Path`` objects now in the API
- The stubs are gone, we are now using annotations
- ``namedexpr_test`` nodes are now a proper class called ``NamedExpr``
- A lot of smaller refactorings

0.7.1 (2020-07-24)
++++++++++++++++++

- Fixed a couple of smaller bugs (mostly syntax error detection in
  ``Grammar.iter_errors``)

This is going to be the last release that supports Python 2.7, 3.4 and 3.5.

0.7.0 (2020-04-13)
++++++++++++++++++

- Fix a lot of annoying bugs in the diff parser. The fuzzer did not find
  issues anymore even after running it for more than 24 hours (500k tests).
- Small grammar change: suites can now contain newlines even after a newline.
  This should really not matter if you don't use error recovery. It allows for
  nicer error recovery.

0.6.2 (2020-02-27)
++++++++++++++++++

- Bugfixes
- Add Grammar.refactor (might still be subject to change until 0.7.0)

0.6.1 (2020-02-03)
++++++++++++++++++

- Add ``parso.normalizer.Issue.end_pos`` to make it possible to know where an
  issue ends

0.6.0 (2020-01-26)
++++++++++++++++++

- Dropped Python 2.6/Python 3.3 support
- del_stmt names are now considered as a definition
  (for ``name.is_definition()``)
- Bugfixes

0.5.2 (2019-12-15)
++++++++++++++++++

- Add include_setitem to get_definition/is_definition and get_defined_names (#66)
- Fix named expression error listing (#89, #90)
- Fix some f-string tokenizer issues (#93)

0.5.1 (2019-07-13)
++++++++++++++++++

- Fix: Some unicode identifiers were not correctly tokenized
- Fix: Line continuations in f-strings are now working

0.5.0 (2019-06-20)
++++++++++++++++++

- **Breaking Change** comp_for is now called sync_comp_for for all Python
  versions to be compatible with the Python 3.8 Grammar
- Added .pyi stubs for a lot of the parso API
- Small FileIO changes

0.4.0 (2019-04-05)
++++++++++++++++++

- Python 3.8 support
- FileIO support, it's now possible to use abstract file IO, support is alpha

0.3.4 (2019-02-13)
+++++++++++++++++++

- Fix an f-string tokenizer error

0.3.3 (2019-02-06)
+++++++++++++++++++

- Fix async errors in the diff parser
- A fix in iter_errors
- This is a very small bugfix release

0.3.2 (2019-01-24)
+++++++++++++++++++

- 20+ bugfixes in the diff parser and 3 in the tokenizer
- A fuzzer for the diff parser, to give confidence that the diff parser is in a
  good shape.
- Some bugfixes for f-string

0.3.1 (2018-07-09)
+++++++++++++++++++

- Bugfixes in the diff parser and keyword-only arguments

0.3.0 (2018-06-30)
+++++++++++++++++++

- Rewrote the pgen2 parser generator.

0.2.1 (2018-05-21)
+++++++++++++++++++

- A bugfix for the diff parser.
- Grammar files can now be loaded from a specific path.

0.2.0 (2018-04-15)
+++++++++++++++++++

- f-strings are now parsed as a part of the normal Python grammar. This makes
  it way easier to deal with them.

0.1.1 (2017-11-05)
+++++++++++++++++++

- Fixed a few bugs in the caching layer
- Added support for Python 3.7

0.1.0 (2017-09-04)
+++++++++++++++++++

- Pulling the library out of Jedi. Some APIs will definitely change.


