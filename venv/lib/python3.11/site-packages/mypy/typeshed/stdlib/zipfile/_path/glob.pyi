import sys
from collections.abc import Iterator
from re import Match

if sys.version_info >= (3, 13):
    class Translator:
        def __init__(self, seps: str = ...) -> None: ...
        def translate(self, pattern: str) -> str: ...
        def extend(self, pattern: str) -> str: ...
        def match_dirs(self, pattern: str) -> str: ...
        def translate_core(self, pattern: str) -> str: ...
        def replace(self, match: Match[str]) -> str: ...
        def restrict_rglob(self, pattern: str) -> None: ...
        def star_not_empty(self, pattern: str) -> str: ...

else:
    def translate(pattern: str) -> str: ...
    def match_dirs(pattern: str) -> str: ...
    def translate_core(pattern: str) -> str: ...
    def replace(match: Match[str]) -> str: ...

def separate(pattern: str) -> Iterator[Match[str]]: ...
