import sys
from codeop import <PERSON><PERSON>ompiler
from collections.abc import Callable, Mapping
from types import CodeType
from typing import Any

__all__ = ["InteractiveInterpreter", "InteractiveConsole", "interact", "compile_command"]

class InteractiveInterpreter:
    locals: Mapping[str, Any]  # undocumented
    compile: CommandCompiler  # undocumented
    def __init__(self, locals: Mapping[str, Any] | None = None) -> None: ...
    def runsource(self, source: str, filename: str = "<input>", symbol: str = "single") -> bool: ...
    def runcode(self, code: CodeType) -> None: ...
    if sys.version_info >= (3, 13):
        def showsyntaxerror(self, filename: str | None = None, *, source: str = "") -> None: ...
    else:
        def showsyntaxerror(self, filename: str | None = None) -> None: ...

    def showtraceback(self) -> None: ...
    def write(self, data: str) -> None: ...

class InteractiveConsole(InteractiveInterpreter):
    buffer: list[str]  # undocumented
    filename: str  # undocumented
    if sys.version_info >= (3, 13):
        def __init__(
            self, locals: Mapping[str, Any] | None = None, filename: str = "<console>", *, local_exit: bool = False
        ) -> None: ...
        def push(self, line: str, filename: str | None = None) -> bool: ...
    else:
        def __init__(self, locals: Mapping[str, Any] | None = None, filename: str = "<console>") -> None: ...
        def push(self, line: str) -> bool: ...

    def interact(self, banner: str | None = None, exitmsg: str | None = None) -> None: ...
    def resetbuffer(self) -> None: ...
    def raw_input(self, prompt: str = "") -> str: ...

if sys.version_info >= (3, 13):
    def interact(
        banner: str | None = None,
        readfunc: Callable[[str], str] | None = None,
        local: Mapping[str, Any] | None = None,
        exitmsg: str | None = None,
        local_exit: bool = False,
    ) -> None: ...

else:
    def interact(
        banner: str | None = None,
        readfunc: Callable[[str], str] | None = None,
        local: Mapping[str, Any] | None = None,
        exitmsg: str | None = None,
    ) -> None: ...

def compile_command(source: str, filename: str = "<input>", symbol: str = "single") -> CodeType | None: ...
