import asyncore
from abc import abstractmethod

class simple_producer:
    def __init__(self, data: bytes, buffer_size: int = 512) -> None: ...
    def more(self) -> bytes: ...

class async_chat(asyncore.dispatcher):
    ac_in_buffer_size: int
    ac_out_buffer_size: int
    @abstractmethod
    def collect_incoming_data(self, data: bytes) -> None: ...
    @abstractmethod
    def found_terminator(self) -> None: ...
    def set_terminator(self, term: bytes | int | None) -> None: ...
    def get_terminator(self) -> bytes | int | None: ...
    def push(self, data: bytes) -> None: ...
    def push_with_producer(self, producer: simple_producer) -> None: ...
    def close_when_done(self) -> None: ...
    def initiate_send(self) -> None: ...
    def discard_buffers(self) -> None: ...
