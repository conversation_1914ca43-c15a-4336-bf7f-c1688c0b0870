import sys
from typing import <PERSON><PERSON>eturn
from xml.sax import xmlreader

version: str

class ErrorHandler:
    def error(self, exception: BaseException) -> NoReturn: ...
    def fatalError(self, exception: BaseException) -> NoReturn: ...
    def warning(self, exception: BaseException) -> None: ...

class ContentHandler:
    def setDocumentLocator(self, locator: xmlreader.Locator) -> None: ...
    def startDocument(self) -> None: ...
    def endDocument(self) -> None: ...
    def startPrefixMapping(self, prefix: str | None, uri: str) -> None: ...
    def endPrefixMapping(self, prefix: str | None) -> None: ...
    def startElement(self, name: str, attrs: xmlreader.AttributesImpl) -> None: ...
    def endElement(self, name: str) -> None: ...
    def startElementNS(self, name: tuple[str, str], qname: str, attrs: xmlreader.AttributesNSImpl) -> None: ...
    def endElementNS(self, name: tuple[str, str], qname: str) -> None: ...
    def characters(self, content: str) -> None: ...
    def ignorableWhitespace(self, whitespace: str) -> None: ...
    def processingInstruction(self, target: str, data: str) -> None: ...
    def skippedEntity(self, name: str) -> None: ...

class DTDHandler:
    def notationDecl(self, name, publicId, systemId): ...
    def unparsedEntityDecl(self, name, publicId, systemId, ndata): ...

class EntityResolver:
    def resolveEntity(self, publicId, systemId): ...

feature_namespaces: str
feature_namespace_prefixes: str
feature_string_interning: str
feature_validation: str
feature_external_ges: str
feature_external_pes: str
all_features: list[str]
property_lexical_handler: str
property_declaration_handler: str
property_dom_node: str
property_xml_string: str
property_encoding: str
property_interning_dict: str
all_properties: list[str]

if sys.version_info >= (3, 10):
    class LexicalHandler:
        def comment(self, content: str) -> object: ...
        def startDTD(self, name: str, public_id: str | None, system_id: str | None) -> object: ...
        def endDTD(self) -> object: ...
        def startCDATA(self) -> object: ...
        def endCDATA(self) -> object: ...
