from collections.abc import Mapping
from typing import overload
from typing_extensions import Self, TypeAlias
from xml.sax.handler import <PERSON><PERSON><PERSON><PERSON>, DTD<PERSON>and<PERSON>, EntityResolver, ErrorHandler

class XMLReader:
    def parse(self, source): ...
    def getContentHandler(self) -> ContentHandler: ...
    def setContentHandler(self, handler: ContentHandler) -> None: ...
    def getDTDHandler(self) -> DTDHandler: ...
    def setDTDHandler(self, handler: DTDHandler) -> None: ...
    def getEntityResolver(self) -> EntityResolver: ...
    def setEntityResolver(self, resolver: EntityResolver) -> None: ...
    def getErrorHandler(self) -> ErrorHandler: ...
    def setErrorHandler(self, handler: ErrorHandler) -> None: ...
    def setLocale(self, locale): ...
    def getFeature(self, name: str) -> object: ...
    def setFeature(self, name: str, state: object) -> None: ...
    def getProperty(self, name: str) -> object: ...
    def setProperty(self, name: str, value: object) -> None: ...

class IncrementalParser(XMLReader):
    def __init__(self, bufsize: int = 65536) -> None: ...
    def parse(self, source): ...
    def feed(self, data): ...
    def prepareParser(self, source): ...
    def close(self): ...
    def reset(self): ...

class Locator:
    def getColumnNumber(self): ...
    def getLineNumber(self): ...
    def getPublicId(self): ...
    def getSystemId(self): ...

class InputSource:
    def __init__(self, system_id: str | None = None) -> None: ...
    def setPublicId(self, public_id): ...
    def getPublicId(self): ...
    def setSystemId(self, system_id): ...
    def getSystemId(self): ...
    def setEncoding(self, encoding): ...
    def getEncoding(self): ...
    def setByteStream(self, bytefile): ...
    def getByteStream(self): ...
    def setCharacterStream(self, charfile): ...
    def getCharacterStream(self): ...

class AttributesImpl:
    def __init__(self, attrs: Mapping[str, str]) -> None: ...
    def getLength(self) -> int: ...
    def getType(self, name: str) -> str: ...
    def getValue(self, name: str) -> str: ...
    def getValueByQName(self, name: str) -> str: ...
    def getNameByQName(self, name: str) -> str: ...
    def getQNameByName(self, name: str) -> str: ...
    def getNames(self) -> list[str]: ...
    def getQNames(self) -> list[str]: ...
    def __len__(self) -> int: ...
    def __getitem__(self, name: str) -> str: ...
    def keys(self) -> list[str]: ...
    def __contains__(self, name: str) -> bool: ...
    @overload
    def get(self, name: str, alternative: None = None) -> str | None: ...
    @overload
    def get(self, name: str, alternative: str) -> str: ...
    def copy(self) -> Self: ...
    def items(self) -> list[tuple[str, str]]: ...
    def values(self) -> list[str]: ...

_NSName: TypeAlias = tuple[str | None, str]

class AttributesNSImpl(AttributesImpl):
    def __init__(self, attrs: Mapping[_NSName, str], qnames: Mapping[_NSName, str]) -> None: ...
    def getType(self, name: _NSName) -> str: ...  # type: ignore[override]
    def getValue(self, name: _NSName) -> str: ...  # type: ignore[override]
    def getNameByQName(self, name: str) -> _NSName: ...  # type: ignore[override]
    def getQNameByName(self, name: _NSName) -> str: ...  # type: ignore[override]
    def getNames(self) -> list[_NSName]: ...  # type: ignore[override]
    def __getitem__(self, name: _NSName) -> str: ...  # type: ignore[override]
    def keys(self) -> list[_NSName]: ...  # type: ignore[override]
    def __contains__(self, name: _NSName) -> bool: ...  # type: ignore[override]
    @overload  # type: ignore[override]
    def get(self, name: _NSName, alternative: None = None) -> str | None: ...
    @overload
    def get(self, name: _NSName, alternative: str) -> str: ...
    def items(self) -> list[tuple[_NSName, str]]: ...  # type: ignore[override]
