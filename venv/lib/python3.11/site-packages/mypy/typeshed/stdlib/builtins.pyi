# ruff: noqa: PYI036 # This is the module declaring BaseException
import _ast
import _sitebuiltins
import _typeshed
import sys
import types
from _collections_abc import dict_items, dict_keys, dict_values
from _typeshed import (
    AnyStr_co,
    ConvertibleToFloat,
    ConvertibleToInt,
    FileDescriptorOrPath,
    MaybeNone,
    OpenBinaryMode,
    OpenBinaryModeReading,
    OpenBinaryModeUpdating,
    OpenBinaryModeWriting,
    OpenTextMode,
    ReadableBuffer,
    SupportsAdd,
    SupportsAiter,
    SupportsAnext,
    SupportsDivMod,
    SupportsFlush,
    SupportsIter,
    SupportsKeysAndGetItem,
    SupportsLenAndGetItem,
    SupportsNext,
    SupportsRAdd,
    SupportsRDivMod,
    SupportsRichComparison,
    SupportsRichComparisonT,
    SupportsWrite,
)
from collections.abc import Awaitable, Callable, Iterable, Iterator, MutableSet, Reversible, Set as AbstractSet, Sized
from io import BufferedRandom, BufferedReader, BufferedWriter, <PERSON><PERSON>, Text<PERSON><PERSON>rapper
from types import CellType, CodeType, TracebackType

# mypy crashes if any of {ByteString, Sequence, MutableSequence, Mapping, MutableMapping}
# are imported from collections.abc in builtins.pyi
from typing import (  # noqa: Y022
    IO,
    Any,
    BinaryIO,
    ClassVar,
    Generic,
    Mapping,
    MutableMapping,
    MutableSequence,
    Protocol,
    Sequence,
    SupportsAbs,
    SupportsBytes,
    SupportsComplex,
    SupportsFloat,
    SupportsIndex,
    TypeVar,
    final,
    overload,
    type_check_only,
)

# we can't import `Literal` from typing or mypy crashes: see #11247
from typing_extensions import (  # noqa: Y023
    Concatenate,
    Literal,
    ParamSpec,
    Self,
    TypeAlias,
    TypeGuard,
    TypeIs,
    TypeVarTuple,
    deprecated,
)

if sys.version_info >= (3, 9):
    from types import GenericAlias

_T = TypeVar("_T")
_I = TypeVar("_I", default=int)
_T_co = TypeVar("_T_co", covariant=True)
_T_contra = TypeVar("_T_contra", contravariant=True)
_R_co = TypeVar("_R_co", covariant=True)
_KT = TypeVar("_KT")
_VT = TypeVar("_VT")
_S = TypeVar("_S")
_T1 = TypeVar("_T1")
_T2 = TypeVar("_T2")
_T3 = TypeVar("_T3")
_T4 = TypeVar("_T4")
_T5 = TypeVar("_T5")
_SupportsNextT = TypeVar("_SupportsNextT", bound=SupportsNext[Any], covariant=True)
_SupportsAnextT = TypeVar("_SupportsAnextT", bound=SupportsAnext[Any], covariant=True)
_AwaitableT = TypeVar("_AwaitableT", bound=Awaitable[Any])
_AwaitableT_co = TypeVar("_AwaitableT_co", bound=Awaitable[Any], covariant=True)
_P = ParamSpec("_P")
_StartT = TypeVar("_StartT", covariant=True, default=Any)
_StopT = TypeVar("_StopT", covariant=True, default=Any)
_StepT = TypeVar("_StepT", covariant=True, default=Any)

class object:
    __doc__: str | None
    __dict__: dict[str, Any]
    __module__: str
    __annotations__: dict[str, Any]
    @property
    def __class__(self) -> type[Self]: ...
    @__class__.setter
    def __class__(self, type: type[Self], /) -> None: ...
    def __init__(self) -> None: ...
    def __new__(cls) -> Self: ...
    # N.B. `object.__setattr__` and `object.__delattr__` are heavily special-cased by type checkers.
    # Overriding them in subclasses has different semantics, even if the override has an identical signature.
    def __setattr__(self, name: str, value: Any, /) -> None: ...
    def __delattr__(self, name: str, /) -> None: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __ne__(self, value: object, /) -> bool: ...
    def __str__(self) -> str: ...  # noqa: Y029
    def __repr__(self) -> str: ...  # noqa: Y029
    def __hash__(self) -> int: ...
    def __format__(self, format_spec: str, /) -> str: ...
    def __getattribute__(self, name: str, /) -> Any: ...
    def __sizeof__(self) -> int: ...
    # return type of pickle methods is rather hard to express in the current type system
    # see #6661 and https://docs.python.org/3/library/pickle.html#object.__reduce__
    def __reduce__(self) -> str | tuple[Any, ...]: ...
    def __reduce_ex__(self, protocol: SupportsIndex, /) -> str | tuple[Any, ...]: ...
    if sys.version_info >= (3, 11):
        def __getstate__(self) -> object: ...

    def __dir__(self) -> Iterable[str]: ...
    def __init_subclass__(cls) -> None: ...
    @classmethod
    def __subclasshook__(cls, subclass: type, /) -> bool: ...

class staticmethod(Generic[_P, _R_co]):
    @property
    def __func__(self) -> Callable[_P, _R_co]: ...
    @property
    def __isabstractmethod__(self) -> bool: ...
    def __init__(self, f: Callable[_P, _R_co], /) -> None: ...
    @overload
    def __get__(self, instance: None, owner: type, /) -> Callable[_P, _R_co]: ...
    @overload
    def __get__(self, instance: _T, owner: type[_T] | None = None, /) -> Callable[_P, _R_co]: ...
    if sys.version_info >= (3, 10):
        __name__: str
        __qualname__: str
        @property
        def __wrapped__(self) -> Callable[_P, _R_co]: ...
        def __call__(self, *args: _P.args, **kwargs: _P.kwargs) -> _R_co: ...

class classmethod(Generic[_T, _P, _R_co]):
    @property
    def __func__(self) -> Callable[Concatenate[type[_T], _P], _R_co]: ...
    @property
    def __isabstractmethod__(self) -> bool: ...
    def __init__(self, f: Callable[Concatenate[type[_T], _P], _R_co], /) -> None: ...
    @overload
    def __get__(self, instance: _T, owner: type[_T] | None = None, /) -> Callable[_P, _R_co]: ...
    @overload
    def __get__(self, instance: None, owner: type[_T], /) -> Callable[_P, _R_co]: ...
    if sys.version_info >= (3, 10):
        __name__: str
        __qualname__: str
        @property
        def __wrapped__(self) -> Callable[Concatenate[type[_T], _P], _R_co]: ...

class type:
    # object.__base__ is None. Otherwise, it would be a type.
    @property
    def __base__(self) -> type | None: ...
    __bases__: tuple[type, ...]
    @property
    def __basicsize__(self) -> int: ...
    @property
    def __dict__(self) -> types.MappingProxyType[str, Any]: ...  # type: ignore[override]
    @property
    def __dictoffset__(self) -> int: ...
    @property
    def __flags__(self) -> int: ...
    @property
    def __itemsize__(self) -> int: ...
    __module__: str
    @property
    def __mro__(self) -> tuple[type, ...]: ...
    __name__: str
    __qualname__: str
    @property
    def __text_signature__(self) -> str | None: ...
    @property
    def __weakrefoffset__(self) -> int: ...
    @overload
    def __init__(self, o: object, /) -> None: ...
    @overload
    def __init__(self, name: str, bases: tuple[type, ...], dict: dict[str, Any], /, **kwds: Any) -> None: ...
    @overload
    def __new__(cls, o: object, /) -> type: ...
    @overload
    def __new__(
        cls: type[_typeshed.Self], name: str, bases: tuple[type, ...], namespace: dict[str, Any], /, **kwds: Any
    ) -> _typeshed.Self: ...
    def __call__(self, *args: Any, **kwds: Any) -> Any: ...
    def __subclasses__(self: _typeshed.Self) -> list[_typeshed.Self]: ...
    # Note: the documentation doesn't specify what the return type is, the standard
    # implementation seems to be returning a list.
    def mro(self) -> list[type]: ...
    def __instancecheck__(self, instance: Any, /) -> bool: ...
    def __subclasscheck__(self, subclass: type, /) -> bool: ...
    @classmethod
    def __prepare__(metacls, name: str, bases: tuple[type, ...], /, **kwds: Any) -> MutableMapping[str, object]: ...
    if sys.version_info >= (3, 10):
        def __or__(self, value: Any, /) -> types.UnionType: ...
        def __ror__(self, value: Any, /) -> types.UnionType: ...
    if sys.version_info >= (3, 12):
        __type_params__: tuple[TypeVar | ParamSpec | TypeVarTuple, ...]

class super:
    @overload
    def __init__(self, t: Any, obj: Any, /) -> None: ...
    @overload
    def __init__(self, t: Any, /) -> None: ...
    @overload
    def __init__(self) -> None: ...

_PositiveInteger: TypeAlias = Literal[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
_NegativeInteger: TypeAlias = Literal[-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20]
_LiteralInteger = _PositiveInteger | _NegativeInteger | Literal[0]  # noqa: Y026  # TODO: Use TypeAlias once mypy bugs are fixed

class int:
    @overload
    def __new__(cls, x: ConvertibleToInt = ..., /) -> Self: ...
    @overload
    def __new__(cls, x: str | bytes | bytearray, /, base: SupportsIndex) -> Self: ...
    def as_integer_ratio(self) -> tuple[int, Literal[1]]: ...
    @property
    def real(self) -> int: ...
    @property
    def imag(self) -> Literal[0]: ...
    @property
    def numerator(self) -> int: ...
    @property
    def denominator(self) -> Literal[1]: ...
    def conjugate(self) -> int: ...
    def bit_length(self) -> int: ...
    if sys.version_info >= (3, 10):
        def bit_count(self) -> int: ...

    if sys.version_info >= (3, 11):
        def to_bytes(
            self, length: SupportsIndex = 1, byteorder: Literal["little", "big"] = "big", *, signed: bool = False
        ) -> bytes: ...
        @classmethod
        def from_bytes(
            cls,
            bytes: Iterable[SupportsIndex] | SupportsBytes | ReadableBuffer,
            byteorder: Literal["little", "big"] = "big",
            *,
            signed: bool = False,
        ) -> Self: ...
    else:
        def to_bytes(self, length: SupportsIndex, byteorder: Literal["little", "big"], *, signed: bool = False) -> bytes: ...
        @classmethod
        def from_bytes(
            cls,
            bytes: Iterable[SupportsIndex] | SupportsBytes | ReadableBuffer,
            byteorder: Literal["little", "big"],
            *,
            signed: bool = False,
        ) -> Self: ...

    if sys.version_info >= (3, 12):
        def is_integer(self) -> Literal[True]: ...

    def __add__(self, value: int, /) -> int: ...
    def __sub__(self, value: int, /) -> int: ...
    def __mul__(self, value: int, /) -> int: ...
    def __floordiv__(self, value: int, /) -> int: ...
    def __truediv__(self, value: int, /) -> float: ...
    def __mod__(self, value: int, /) -> int: ...
    def __divmod__(self, value: int, /) -> tuple[int, int]: ...
    def __radd__(self, value: int, /) -> int: ...
    def __rsub__(self, value: int, /) -> int: ...
    def __rmul__(self, value: int, /) -> int: ...
    def __rfloordiv__(self, value: int, /) -> int: ...
    def __rtruediv__(self, value: int, /) -> float: ...
    def __rmod__(self, value: int, /) -> int: ...
    def __rdivmod__(self, value: int, /) -> tuple[int, int]: ...
    @overload
    def __pow__(self, x: Literal[0], /) -> Literal[1]: ...
    @overload
    def __pow__(self, value: Literal[0], mod: None, /) -> Literal[1]: ...
    @overload
    def __pow__(self, value: _PositiveInteger, mod: None = None, /) -> int: ...
    @overload
    def __pow__(self, value: _NegativeInteger, mod: None = None, /) -> float: ...
    # positive __value -> int; negative __value -> float
    # return type must be Any as `int | float` causes too many false-positive errors
    @overload
    def __pow__(self, value: int, mod: None = None, /) -> Any: ...
    @overload
    def __pow__(self, value: int, mod: int, /) -> int: ...
    def __rpow__(self, value: int, mod: int | None = None, /) -> Any: ...
    def __and__(self, value: int, /) -> int: ...
    def __or__(self, value: int, /) -> int: ...
    def __xor__(self, value: int, /) -> int: ...
    def __lshift__(self, value: int, /) -> int: ...
    def __rshift__(self, value: int, /) -> int: ...
    def __rand__(self, value: int, /) -> int: ...
    def __ror__(self, value: int, /) -> int: ...
    def __rxor__(self, value: int, /) -> int: ...
    def __rlshift__(self, value: int, /) -> int: ...
    def __rrshift__(self, value: int, /) -> int: ...
    def __neg__(self) -> int: ...
    def __pos__(self) -> int: ...
    def __invert__(self) -> int: ...
    def __trunc__(self) -> int: ...
    def __ceil__(self) -> int: ...
    def __floor__(self) -> int: ...
    def __round__(self, ndigits: SupportsIndex = ..., /) -> int: ...
    def __getnewargs__(self) -> tuple[int]: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __ne__(self, value: object, /) -> bool: ...
    def __lt__(self, value: int, /) -> bool: ...
    def __le__(self, value: int, /) -> bool: ...
    def __gt__(self, value: int, /) -> bool: ...
    def __ge__(self, value: int, /) -> bool: ...
    def __float__(self) -> float: ...
    def __int__(self) -> int: ...
    def __abs__(self) -> int: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...
    def __index__(self) -> int: ...

class float:
    def __new__(cls, x: ConvertibleToFloat = ..., /) -> Self: ...
    def as_integer_ratio(self) -> tuple[int, int]: ...
    def hex(self) -> str: ...
    def is_integer(self) -> bool: ...
    @classmethod
    def fromhex(cls, string: str, /) -> Self: ...
    @property
    def real(self) -> float: ...
    @property
    def imag(self) -> float: ...
    def conjugate(self) -> float: ...
    def __add__(self, value: float, /) -> float: ...
    def __sub__(self, value: float, /) -> float: ...
    def __mul__(self, value: float, /) -> float: ...
    def __floordiv__(self, value: float, /) -> float: ...
    def __truediv__(self, value: float, /) -> float: ...
    def __mod__(self, value: float, /) -> float: ...
    def __divmod__(self, value: float, /) -> tuple[float, float]: ...
    @overload
    def __pow__(self, value: int, mod: None = None, /) -> float: ...
    # positive __value -> float; negative __value -> complex
    # return type must be Any as `float | complex` causes too many false-positive errors
    @overload
    def __pow__(self, value: float, mod: None = None, /) -> Any: ...
    def __radd__(self, value: float, /) -> float: ...
    def __rsub__(self, value: float, /) -> float: ...
    def __rmul__(self, value: float, /) -> float: ...
    def __rfloordiv__(self, value: float, /) -> float: ...
    def __rtruediv__(self, value: float, /) -> float: ...
    def __rmod__(self, value: float, /) -> float: ...
    def __rdivmod__(self, value: float, /) -> tuple[float, float]: ...
    @overload
    def __rpow__(self, value: _PositiveInteger, mod: None = None, /) -> float: ...
    @overload
    def __rpow__(self, value: _NegativeInteger, mod: None = None, /) -> complex: ...
    # Returning `complex` for the general case gives too many false-positive errors.
    @overload
    def __rpow__(self, value: float, mod: None = None, /) -> Any: ...
    def __getnewargs__(self) -> tuple[float]: ...
    def __trunc__(self) -> int: ...
    if sys.version_info >= (3, 9):
        def __ceil__(self) -> int: ...
        def __floor__(self) -> int: ...

    @overload
    def __round__(self, ndigits: None = None, /) -> int: ...
    @overload
    def __round__(self, ndigits: SupportsIndex, /) -> float: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __ne__(self, value: object, /) -> bool: ...
    def __lt__(self, value: float, /) -> bool: ...
    def __le__(self, value: float, /) -> bool: ...
    def __gt__(self, value: float, /) -> bool: ...
    def __ge__(self, value: float, /) -> bool: ...
    def __neg__(self) -> float: ...
    def __pos__(self) -> float: ...
    def __int__(self) -> int: ...
    def __float__(self) -> float: ...
    def __abs__(self) -> float: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...

class complex:
    # Python doesn't currently accept SupportsComplex for the second argument
    @overload
    def __new__(
        cls,
        real: complex | SupportsComplex | SupportsFloat | SupportsIndex = ...,
        imag: complex | SupportsFloat | SupportsIndex = ...,
    ) -> Self: ...
    @overload
    def __new__(cls, real: str | SupportsComplex | SupportsFloat | SupportsIndex | complex) -> Self: ...
    @property
    def real(self) -> float: ...
    @property
    def imag(self) -> float: ...
    def conjugate(self) -> complex: ...
    def __add__(self, value: complex, /) -> complex: ...
    def __sub__(self, value: complex, /) -> complex: ...
    def __mul__(self, value: complex, /) -> complex: ...
    def __pow__(self, value: complex, mod: None = None, /) -> complex: ...
    def __truediv__(self, value: complex, /) -> complex: ...
    def __radd__(self, value: complex, /) -> complex: ...
    def __rsub__(self, value: complex, /) -> complex: ...
    def __rmul__(self, value: complex, /) -> complex: ...
    def __rpow__(self, value: complex, mod: None = None, /) -> complex: ...
    def __rtruediv__(self, value: complex, /) -> complex: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __ne__(self, value: object, /) -> bool: ...
    def __neg__(self) -> complex: ...
    def __pos__(self) -> complex: ...
    def __abs__(self) -> float: ...
    def __hash__(self) -> int: ...
    def __bool__(self) -> bool: ...
    if sys.version_info >= (3, 11):
        def __complex__(self) -> complex: ...

class _FormatMapMapping(Protocol):
    def __getitem__(self, key: str, /) -> Any: ...

class _TranslateTable(Protocol):
    def __getitem__(self, key: int, /) -> str | int | None: ...

class str(Sequence[str]):
    @overload
    def __new__(cls, object: object = ...) -> Self: ...
    @overload
    def __new__(cls, object: ReadableBuffer, encoding: str = ..., errors: str = ...) -> Self: ...
    def capitalize(self) -> str: ...  # type: ignore[misc]
    def casefold(self) -> str: ...  # type: ignore[misc]
    def center(self, width: SupportsIndex, fillchar: str = " ", /) -> str: ...  # type: ignore[misc]
    def count(self, sub: str, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /) -> int: ...
    def encode(self, encoding: str = "utf-8", errors: str = "strict") -> bytes: ...
    def endswith(
        self, suffix: str | tuple[str, ...], start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> bool: ...
    def expandtabs(self, tabsize: SupportsIndex = 8) -> str: ...  # type: ignore[misc]
    def find(self, sub: str, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /) -> int: ...
    def format(self, *args: object, **kwargs: object) -> str: ...
    def format_map(self, mapping: _FormatMapMapping, /) -> str: ...
    def index(self, sub: str, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /) -> int: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    def isascii(self) -> bool: ...
    def isdecimal(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def isidentifier(self) -> bool: ...
    def islower(self) -> bool: ...
    def isnumeric(self) -> bool: ...
    def isprintable(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, iterable: Iterable[str], /) -> str: ...  # type: ignore[misc]
    def ljust(self, width: SupportsIndex, fillchar: str = " ", /) -> str: ...  # type: ignore[misc]
    def lower(self) -> str: ...  # type: ignore[misc]
    def lstrip(self, chars: str | None = None, /) -> str: ...  # type: ignore[misc]
    def partition(self, sep: str, /) -> tuple[str, str, str]: ...  # type: ignore[misc]
    if sys.version_info >= (3, 13):
        def replace(self, old: str, new: str, /, count: SupportsIndex = -1) -> str: ...  # type: ignore[misc]
    else:
        def replace(self, old: str, new: str, count: SupportsIndex = -1, /) -> str: ...  # type: ignore[misc]
    if sys.version_info >= (3, 9):
        def removeprefix(self, prefix: str, /) -> str: ...  # type: ignore[misc]
        def removesuffix(self, suffix: str, /) -> str: ...  # type: ignore[misc]

    def rfind(self, sub: str, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /) -> int: ...
    def rindex(self, sub: str, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /) -> int: ...
    def rjust(self, width: SupportsIndex, fillchar: str = " ", /) -> str: ...  # type: ignore[misc]
    def rpartition(self, sep: str, /) -> tuple[str, str, str]: ...  # type: ignore[misc]
    def rsplit(self, sep: str | None = None, maxsplit: SupportsIndex = -1) -> list[str]: ...  # type: ignore[misc]
    def rstrip(self, chars: str | None = None, /) -> str: ...  # type: ignore[misc]
    def split(self, sep: str | None = None, maxsplit: SupportsIndex = -1) -> list[str]: ...  # type: ignore[misc]
    def splitlines(self, keepends: bool = False) -> list[str]: ...  # type: ignore[misc]
    def startswith(
        self, prefix: str | tuple[str, ...], start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> bool: ...
    def strip(self, chars: str | None = None, /) -> str: ...  # type: ignore[misc]
    def swapcase(self) -> str: ...  # type: ignore[misc]
    def title(self) -> str: ...  # type: ignore[misc]
    def translate(self, table: _TranslateTable, /) -> str: ...
    def upper(self) -> str: ...  # type: ignore[misc]
    def zfill(self, width: SupportsIndex, /) -> str: ...  # type: ignore[misc]
    @staticmethod
    @overload
    def maketrans(x: dict[int, _T] | dict[str, _T] | dict[str | int, _T], /) -> dict[int, _T]: ...
    @staticmethod
    @overload
    def maketrans(x: str, y: str, /) -> dict[int, int]: ...
    @staticmethod
    @overload
    def maketrans(x: str, y: str, z: str, /) -> dict[int, int | None]: ...
    def __add__(self, value: str, /) -> str: ...  # type: ignore[misc]
    # Incompatible with Sequence.__contains__
    def __contains__(self, key: str, /) -> bool: ...  # type: ignore[override]
    def __eq__(self, value: object, /) -> bool: ...
    def __ge__(self, value: str, /) -> bool: ...
    def __getitem__(self, key: SupportsIndex | slice, /) -> str: ...
    def __gt__(self, value: str, /) -> bool: ...
    def __hash__(self) -> int: ...
    def __iter__(self) -> Iterator[str]: ...  # type: ignore[misc]
    def __le__(self, value: str, /) -> bool: ...
    def __len__(self) -> int: ...
    def __lt__(self, value: str, /) -> bool: ...
    def __mod__(self, value: Any, /) -> str: ...
    def __mul__(self, value: SupportsIndex, /) -> str: ...  # type: ignore[misc]
    def __ne__(self, value: object, /) -> bool: ...
    def __rmul__(self, value: SupportsIndex, /) -> str: ...  # type: ignore[misc]
    def __getnewargs__(self) -> tuple[str]: ...

class bytes(Sequence[int]):
    @overload
    def __new__(cls, o: Iterable[SupportsIndex] | SupportsIndex | SupportsBytes | ReadableBuffer, /) -> Self: ...
    @overload
    def __new__(cls, string: str, /, encoding: str, errors: str = ...) -> Self: ...
    @overload
    def __new__(cls) -> Self: ...
    def capitalize(self) -> bytes: ...
    def center(self, width: SupportsIndex, fillchar: bytes = b" ", /) -> bytes: ...
    def count(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def decode(self, encoding: str = "utf-8", errors: str = "strict") -> str: ...
    def endswith(
        self,
        suffix: ReadableBuffer | tuple[ReadableBuffer, ...],
        start: SupportsIndex | None = ...,
        end: SupportsIndex | None = ...,
        /,
    ) -> bool: ...
    def expandtabs(self, tabsize: SupportsIndex = 8) -> bytes: ...
    def find(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    def index(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    def isascii(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def islower(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, iterable_of_bytes: Iterable[ReadableBuffer], /) -> bytes: ...
    def ljust(self, width: SupportsIndex, fillchar: bytes | bytearray = b" ", /) -> bytes: ...
    def lower(self) -> bytes: ...
    def lstrip(self, bytes: ReadableBuffer | None = None, /) -> bytes: ...
    def partition(self, sep: ReadableBuffer, /) -> tuple[bytes, bytes, bytes]: ...
    def replace(self, old: ReadableBuffer, new: ReadableBuffer, count: SupportsIndex = -1, /) -> bytes: ...
    if sys.version_info >= (3, 9):
        def removeprefix(self, prefix: ReadableBuffer, /) -> bytes: ...
        def removesuffix(self, suffix: ReadableBuffer, /) -> bytes: ...

    def rfind(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def rindex(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def rjust(self, width: SupportsIndex, fillchar: bytes | bytearray = b" ", /) -> bytes: ...
    def rpartition(self, sep: ReadableBuffer, /) -> tuple[bytes, bytes, bytes]: ...
    def rsplit(self, sep: ReadableBuffer | None = None, maxsplit: SupportsIndex = -1) -> list[bytes]: ...
    def rstrip(self, bytes: ReadableBuffer | None = None, /) -> bytes: ...
    def split(self, sep: ReadableBuffer | None = None, maxsplit: SupportsIndex = -1) -> list[bytes]: ...
    def splitlines(self, keepends: bool = False) -> list[bytes]: ...
    def startswith(
        self,
        prefix: ReadableBuffer | tuple[ReadableBuffer, ...],
        start: SupportsIndex | None = ...,
        end: SupportsIndex | None = ...,
        /,
    ) -> bool: ...
    def strip(self, bytes: ReadableBuffer | None = None, /) -> bytes: ...
    def swapcase(self) -> bytes: ...
    def title(self) -> bytes: ...
    def translate(self, table: ReadableBuffer | None, /, delete: bytes = b"") -> bytes: ...
    def upper(self) -> bytes: ...
    def zfill(self, width: SupportsIndex, /) -> bytes: ...
    @classmethod
    def fromhex(cls, string: str, /) -> Self: ...
    @staticmethod
    def maketrans(frm: ReadableBuffer, to: ReadableBuffer, /) -> bytes: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[int]: ...
    def __hash__(self) -> int: ...
    @overload
    def __getitem__(self, key: SupportsIndex, /) -> int: ...
    @overload
    def __getitem__(self, key: slice, /) -> bytes: ...
    def __add__(self, value: ReadableBuffer, /) -> bytes: ...
    def __mul__(self, value: SupportsIndex, /) -> bytes: ...
    def __rmul__(self, value: SupportsIndex, /) -> bytes: ...
    def __mod__(self, value: Any, /) -> bytes: ...
    # Incompatible with Sequence.__contains__
    def __contains__(self, key: SupportsIndex | ReadableBuffer, /) -> bool: ...  # type: ignore[override]
    def __eq__(self, value: object, /) -> bool: ...
    def __ne__(self, value: object, /) -> bool: ...
    def __lt__(self, value: bytes, /) -> bool: ...
    def __le__(self, value: bytes, /) -> bool: ...
    def __gt__(self, value: bytes, /) -> bool: ...
    def __ge__(self, value: bytes, /) -> bool: ...
    def __getnewargs__(self) -> tuple[bytes]: ...
    if sys.version_info >= (3, 11):
        def __bytes__(self) -> bytes: ...

    def __buffer__(self, flags: int, /) -> memoryview: ...

class bytearray(MutableSequence[int]):
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, ints: Iterable[SupportsIndex] | SupportsIndex | ReadableBuffer, /) -> None: ...
    @overload
    def __init__(self, string: str, /, encoding: str, errors: str = ...) -> None: ...
    def append(self, item: SupportsIndex, /) -> None: ...
    def capitalize(self) -> bytearray: ...
    def center(self, width: SupportsIndex, fillchar: bytes = b" ", /) -> bytearray: ...
    def count(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def copy(self) -> bytearray: ...
    def decode(self, encoding: str = "utf-8", errors: str = "strict") -> str: ...
    def endswith(
        self,
        suffix: ReadableBuffer | tuple[ReadableBuffer, ...],
        start: SupportsIndex | None = ...,
        end: SupportsIndex | None = ...,
        /,
    ) -> bool: ...
    def expandtabs(self, tabsize: SupportsIndex = 8) -> bytearray: ...
    def extend(self, iterable_of_ints: Iterable[SupportsIndex], /) -> None: ...
    def find(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    def index(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def insert(self, index: SupportsIndex, item: SupportsIndex, /) -> None: ...
    def isalnum(self) -> bool: ...
    def isalpha(self) -> bool: ...
    def isascii(self) -> bool: ...
    def isdigit(self) -> bool: ...
    def islower(self) -> bool: ...
    def isspace(self) -> bool: ...
    def istitle(self) -> bool: ...
    def isupper(self) -> bool: ...
    def join(self, iterable_of_bytes: Iterable[ReadableBuffer], /) -> bytearray: ...
    def ljust(self, width: SupportsIndex, fillchar: bytes | bytearray = b" ", /) -> bytearray: ...
    def lower(self) -> bytearray: ...
    def lstrip(self, bytes: ReadableBuffer | None = None, /) -> bytearray: ...
    def partition(self, sep: ReadableBuffer, /) -> tuple[bytearray, bytearray, bytearray]: ...
    def pop(self, index: int = -1, /) -> int: ...
    def remove(self, value: int, /) -> None: ...
    if sys.version_info >= (3, 9):
        def removeprefix(self, prefix: ReadableBuffer, /) -> bytearray: ...
        def removesuffix(self, suffix: ReadableBuffer, /) -> bytearray: ...

    def replace(self, old: ReadableBuffer, new: ReadableBuffer, count: SupportsIndex = -1, /) -> bytearray: ...
    def rfind(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def rindex(
        self, sub: ReadableBuffer | SupportsIndex, start: SupportsIndex | None = ..., end: SupportsIndex | None = ..., /
    ) -> int: ...
    def rjust(self, width: SupportsIndex, fillchar: bytes | bytearray = b" ", /) -> bytearray: ...
    def rpartition(self, sep: ReadableBuffer, /) -> tuple[bytearray, bytearray, bytearray]: ...
    def rsplit(self, sep: ReadableBuffer | None = None, maxsplit: SupportsIndex = -1) -> list[bytearray]: ...
    def rstrip(self, bytes: ReadableBuffer | None = None, /) -> bytearray: ...
    def split(self, sep: ReadableBuffer | None = None, maxsplit: SupportsIndex = -1) -> list[bytearray]: ...
    def splitlines(self, keepends: bool = False) -> list[bytearray]: ...
    def startswith(
        self,
        prefix: ReadableBuffer | tuple[ReadableBuffer, ...],
        start: SupportsIndex | None = ...,
        end: SupportsIndex | None = ...,
        /,
    ) -> bool: ...
    def strip(self, bytes: ReadableBuffer | None = None, /) -> bytearray: ...
    def swapcase(self) -> bytearray: ...
    def title(self) -> bytearray: ...
    def translate(self, table: ReadableBuffer | None, /, delete: bytes = b"") -> bytearray: ...
    def upper(self) -> bytearray: ...
    def zfill(self, width: SupportsIndex, /) -> bytearray: ...
    @classmethod
    def fromhex(cls, string: str, /) -> Self: ...
    @staticmethod
    def maketrans(frm: ReadableBuffer, to: ReadableBuffer, /) -> bytes: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[int]: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    @overload
    def __getitem__(self, key: SupportsIndex, /) -> int: ...
    @overload
    def __getitem__(self, key: slice, /) -> bytearray: ...
    @overload
    def __setitem__(self, key: SupportsIndex, value: SupportsIndex, /) -> None: ...
    @overload
    def __setitem__(self, key: slice, value: Iterable[SupportsIndex] | bytes, /) -> None: ...
    def __delitem__(self, key: SupportsIndex | slice, /) -> None: ...
    def __add__(self, value: ReadableBuffer, /) -> bytearray: ...
    # The superclass wants us to accept Iterable[int], but that fails at runtime.
    def __iadd__(self, value: ReadableBuffer, /) -> Self: ...  # type: ignore[override]
    def __mul__(self, value: SupportsIndex, /) -> bytearray: ...
    def __rmul__(self, value: SupportsIndex, /) -> bytearray: ...
    def __imul__(self, value: SupportsIndex, /) -> Self: ...
    def __mod__(self, value: Any, /) -> bytes: ...
    # Incompatible with Sequence.__contains__
    def __contains__(self, key: SupportsIndex | ReadableBuffer, /) -> bool: ...  # type: ignore[override]
    def __eq__(self, value: object, /) -> bool: ...
    def __ne__(self, value: object, /) -> bool: ...
    def __lt__(self, value: ReadableBuffer, /) -> bool: ...
    def __le__(self, value: ReadableBuffer, /) -> bool: ...
    def __gt__(self, value: ReadableBuffer, /) -> bool: ...
    def __ge__(self, value: ReadableBuffer, /) -> bool: ...
    def __alloc__(self) -> int: ...
    def __buffer__(self, flags: int, /) -> memoryview: ...
    def __release_buffer__(self, buffer: memoryview, /) -> None: ...

_IntegerFormats: TypeAlias = Literal[
    "b", "B", "@b", "@B", "h", "H", "@h", "@H", "i", "I", "@i", "@I", "l", "L", "@l", "@L", "q", "Q", "@q", "@Q", "P", "@P"
]

@final
class memoryview(Sequence[_I]):
    @property
    def format(self) -> str: ...
    @property
    def itemsize(self) -> int: ...
    @property
    def shape(self) -> tuple[int, ...] | None: ...
    @property
    def strides(self) -> tuple[int, ...] | None: ...
    @property
    def suboffsets(self) -> tuple[int, ...] | None: ...
    @property
    def readonly(self) -> bool: ...
    @property
    def ndim(self) -> int: ...
    @property
    def obj(self) -> ReadableBuffer: ...
    @property
    def c_contiguous(self) -> bool: ...
    @property
    def f_contiguous(self) -> bool: ...
    @property
    def contiguous(self) -> bool: ...
    @property
    def nbytes(self) -> int: ...
    def __new__(cls, obj: ReadableBuffer) -> Self: ...
    def __enter__(self) -> Self: ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None, /
    ) -> None: ...
    @overload
    def cast(self, format: Literal["c", "@c"], shape: list[int] | tuple[int, ...] = ...) -> memoryview[bytes]: ...
    @overload
    def cast(self, format: Literal["f", "@f", "d", "@d"], shape: list[int] | tuple[int, ...] = ...) -> memoryview[float]: ...
    @overload
    def cast(self, format: Literal["?"], shape: list[int] | tuple[int, ...] = ...) -> memoryview[bool]: ...
    @overload
    def cast(self, format: _IntegerFormats, shape: list[int] | tuple[int, ...] = ...) -> memoryview: ...
    @overload
    def __getitem__(self, key: SupportsIndex | tuple[SupportsIndex, ...], /) -> _I: ...
    @overload
    def __getitem__(self, key: slice, /) -> memoryview[_I]: ...
    def __contains__(self, x: object, /) -> bool: ...
    def __iter__(self) -> Iterator[_I]: ...
    def __len__(self) -> int: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __hash__(self) -> int: ...
    @overload
    def __setitem__(self, key: slice, value: ReadableBuffer, /) -> None: ...
    @overload
    def __setitem__(self, key: SupportsIndex | tuple[SupportsIndex, ...], value: _I, /) -> None: ...
    if sys.version_info >= (3, 10):
        def tobytes(self, order: Literal["C", "F", "A"] | None = "C") -> bytes: ...
    else:
        def tobytes(self, order: Literal["C", "F", "A"] | None = None) -> bytes: ...

    def tolist(self) -> list[int]: ...
    def toreadonly(self) -> memoryview: ...
    def release(self) -> None: ...
    def hex(self, sep: str | bytes = ..., bytes_per_sep: SupportsIndex = ...) -> str: ...
    def __buffer__(self, flags: int, /) -> memoryview: ...
    def __release_buffer__(self, buffer: memoryview, /) -> None: ...

    # These are inherited from the Sequence ABC, but don't actually exist on memoryview.
    # See https://github.com/python/cpython/issues/125420
    index: ClassVar[None]  # type: ignore[assignment]
    count: ClassVar[None]  # type: ignore[assignment]

@final
class bool(int):
    def __new__(cls, o: object = ..., /) -> Self: ...
    # The following overloads could be represented more elegantly with a TypeVar("_B", bool, int),
    # however mypy has a bug regarding TypeVar constraints (https://github.com/python/mypy/issues/11880).
    @overload
    def __and__(self, value: bool, /) -> bool: ...
    @overload
    def __and__(self, value: int, /) -> int: ...
    @overload
    def __or__(self, value: bool, /) -> bool: ...
    @overload
    def __or__(self, value: int, /) -> int: ...
    @overload
    def __xor__(self, value: bool, /) -> bool: ...
    @overload
    def __xor__(self, value: int, /) -> int: ...
    @overload
    def __rand__(self, value: bool, /) -> bool: ...
    @overload
    def __rand__(self, value: int, /) -> int: ...
    @overload
    def __ror__(self, value: bool, /) -> bool: ...
    @overload
    def __ror__(self, value: int, /) -> int: ...
    @overload
    def __rxor__(self, value: bool, /) -> bool: ...
    @overload
    def __rxor__(self, value: int, /) -> int: ...
    def __getnewargs__(self) -> tuple[int]: ...
    @deprecated("Will throw an error in Python 3.14. Use `not` for logical negation of bools instead.")
    def __invert__(self) -> int: ...

@final
class slice(Generic[_StartT, _StopT, _StepT]):
    @property
    def start(self) -> _StartT: ...
    @property
    def step(self) -> _StepT: ...
    @property
    def stop(self) -> _StopT: ...
    @overload
    def __new__(cls, stop: int | None, /) -> slice[int | MaybeNone, int | MaybeNone, int | MaybeNone]: ...
    @overload
    def __new__(
        cls, start: int | None, stop: int | None, step: int | None = None, /
    ) -> slice[int | MaybeNone, int | MaybeNone, int | MaybeNone]: ...
    @overload
    def __new__(cls, stop: _T2, /) -> slice[Any, _T2, Any]: ...
    @overload
    def __new__(cls, start: _T1, stop: _T2, /) -> slice[_T1, _T2, Any]: ...
    @overload
    def __new__(cls, start: _T1, stop: _T2, step: _T3, /) -> slice[_T1, _T2, _T3]: ...
    def __eq__(self, value: object, /) -> bool: ...
    if sys.version_info >= (3, 12):
        def __hash__(self) -> int: ...
    else:
        __hash__: ClassVar[None]  # type: ignore[assignment]

    def indices(self, len: SupportsIndex, /) -> tuple[int, int, int]: ...

class tuple(Sequence[_T_co]):
    def __new__(cls, iterable: Iterable[_T_co] = ..., /) -> Self: ...
    def __len__(self) -> int: ...
    def __contains__(self, key: object, /) -> bool: ...
    @overload
    def __getitem__(self, key: SupportsIndex, /) -> _T_co: ...
    @overload
    def __getitem__(self, key: slice, /) -> tuple[_T_co, ...]: ...
    def __iter__(self) -> Iterator[_T_co]: ...
    def __lt__(self, value: tuple[_T_co, ...], /) -> bool: ...
    def __le__(self, value: tuple[_T_co, ...], /) -> bool: ...
    def __gt__(self, value: tuple[_T_co, ...], /) -> bool: ...
    def __ge__(self, value: tuple[_T_co, ...], /) -> bool: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __hash__(self) -> int: ...
    @overload
    def __add__(self, value: tuple[_T_co, ...], /) -> tuple[_T_co, ...]: ...
    @overload
    def __add__(self, value: tuple[_T, ...], /) -> tuple[_T_co | _T, ...]: ...
    def __mul__(self, value: SupportsIndex, /) -> tuple[_T_co, ...]: ...
    def __rmul__(self, value: SupportsIndex, /) -> tuple[_T_co, ...]: ...
    def count(self, value: Any, /) -> int: ...
    def index(self, value: Any, start: SupportsIndex = 0, stop: SupportsIndex = sys.maxsize, /) -> int: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

# Doesn't exist at runtime, but deleting this breaks mypy and pyright. See:
# https://github.com/python/typeshed/issues/7580
# https://github.com/python/mypy/issues/8240
@final
@type_check_only
class function:
    # Make sure this class definition stays roughly in line with `types.FunctionType`
    @property
    def __closure__(self) -> tuple[CellType, ...] | None: ...
    __code__: CodeType
    __defaults__: tuple[Any, ...] | None
    __dict__: dict[str, Any]
    @property
    def __globals__(self) -> dict[str, Any]: ...
    __name__: str
    __qualname__: str
    __annotations__: dict[str, Any]
    __kwdefaults__: dict[str, Any]
    if sys.version_info >= (3, 10):
        @property
        def __builtins__(self) -> dict[str, Any]: ...
    if sys.version_info >= (3, 12):
        __type_params__: tuple[TypeVar | ParamSpec | TypeVarTuple, ...]

    __module__: str
    # mypy uses `builtins.function.__get__` to represent methods, properties, and getset_descriptors so we type the return as Any.
    def __get__(self, instance: object, owner: type | None = None, /) -> Any: ...

class list(MutableSequence[_T]):
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, iterable: Iterable[_T], /) -> None: ...
    def copy(self) -> list[_T]: ...
    def append(self, object: _T, /) -> None: ...
    def extend(self, iterable: Iterable[_T], /) -> None: ...
    def pop(self, index: SupportsIndex = -1, /) -> _T: ...
    # Signature of `list.index` should be kept in line with `collections.UserList.index()`
    # and multiprocessing.managers.ListProxy.index()
    def index(self, value: _T, start: SupportsIndex = 0, stop: SupportsIndex = sys.maxsize, /) -> int: ...
    def count(self, value: _T, /) -> int: ...
    def insert(self, index: SupportsIndex, object: _T, /) -> None: ...
    def remove(self, value: _T, /) -> None: ...
    # Signature of `list.sort` should be kept inline with `collections.UserList.sort()`
    # and multiprocessing.managers.ListProxy.sort()
    #
    # Use list[SupportsRichComparisonT] for the first overload rather than [SupportsRichComparison]
    # to work around invariance
    @overload
    def sort(self: list[SupportsRichComparisonT], *, key: None = None, reverse: bool = False) -> None: ...
    @overload
    def sort(self, *, key: Callable[[_T], SupportsRichComparison], reverse: bool = False) -> None: ...
    def __len__(self) -> int: ...
    def __iter__(self) -> Iterator[_T]: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    @overload
    def __getitem__(self, i: SupportsIndex, /) -> _T: ...
    @overload
    def __getitem__(self, s: slice, /) -> list[_T]: ...
    @overload
    def __setitem__(self, key: SupportsIndex, value: _T, /) -> None: ...
    @overload
    def __setitem__(self, key: slice, value: Iterable[_T], /) -> None: ...
    def __delitem__(self, key: SupportsIndex | slice, /) -> None: ...
    # Overloading looks unnecessary, but is needed to work around complex mypy problems
    @overload
    def __add__(self, value: list[_T], /) -> list[_T]: ...
    @overload
    def __add__(self, value: list[_S], /) -> list[_S | _T]: ...
    def __iadd__(self, value: Iterable[_T], /) -> Self: ...  # type: ignore[misc]
    def __mul__(self, value: SupportsIndex, /) -> list[_T]: ...
    def __rmul__(self, value: SupportsIndex, /) -> list[_T]: ...
    def __imul__(self, value: SupportsIndex, /) -> Self: ...
    def __contains__(self, key: object, /) -> bool: ...
    def __reversed__(self) -> Iterator[_T]: ...
    def __gt__(self, value: list[_T], /) -> bool: ...
    def __ge__(self, value: list[_T], /) -> bool: ...
    def __lt__(self, value: list[_T], /) -> bool: ...
    def __le__(self, value: list[_T], /) -> bool: ...
    def __eq__(self, value: object, /) -> bool: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

class dict(MutableMapping[_KT, _VT]):
    # __init__ should be kept roughly in line with `collections.UserDict.__init__`, which has similar semantics
    # Also multiprocessing.managers.SyncManager.dict()
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self: dict[str, _VT], **kwargs: _VT) -> None: ...  # pyright: ignore[reportInvalidTypeVarUse]  #11780
    @overload
    def __init__(self, map: SupportsKeysAndGetItem[_KT, _VT], /) -> None: ...
    @overload
    def __init__(
        self: dict[str, _VT],  # pyright: ignore[reportInvalidTypeVarUse]  #11780
        map: SupportsKeysAndGetItem[str, _VT],
        /,
        **kwargs: _VT,
    ) -> None: ...
    @overload
    def __init__(self, iterable: Iterable[tuple[_KT, _VT]], /) -> None: ...
    @overload
    def __init__(
        self: dict[str, _VT],  # pyright: ignore[reportInvalidTypeVarUse]  #11780
        iterable: Iterable[tuple[str, _VT]],
        /,
        **kwargs: _VT,
    ) -> None: ...
    # Next two overloads are for dict(string.split(sep) for string in iterable)
    # Cannot be Iterable[Sequence[_T]] or otherwise dict(["foo", "bar", "baz"]) is not an error
    @overload
    def __init__(self: dict[str, str], iterable: Iterable[list[str]], /) -> None: ...
    @overload
    def __init__(self: dict[bytes, bytes], iterable: Iterable[list[bytes]], /) -> None: ...
    def __new__(cls, *args: Any, **kwargs: Any) -> Self: ...
    def copy(self) -> dict[_KT, _VT]: ...
    def keys(self) -> dict_keys[_KT, _VT]: ...
    def values(self) -> dict_values[_KT, _VT]: ...
    def items(self) -> dict_items[_KT, _VT]: ...
    # Signature of `dict.fromkeys` should be kept identical to
    # `fromkeys` methods of `OrderedDict`/`ChainMap`/`UserDict` in `collections`
    # TODO: the true signature of `dict.fromkeys` is not expressible in the current type system.
    # See #3800 & https://github.com/python/typing/issues/548#issuecomment-683336963.
    @classmethod
    @overload
    def fromkeys(cls, iterable: Iterable[_T], value: None = None, /) -> dict[_T, Any | None]: ...
    @classmethod
    @overload
    def fromkeys(cls, iterable: Iterable[_T], value: _S, /) -> dict[_T, _S]: ...
    # Positional-only in dict, but not in MutableMapping
    @overload  # type: ignore[override]
    def get(self, key: _KT, /) -> _VT | None: ...
    @overload
    def get(self, key: _KT, default: _VT, /) -> _VT: ...
    @overload
    def get(self, key: _KT, default: _T, /) -> _VT | _T: ...
    @overload
    def pop(self, key: _KT, /) -> _VT: ...
    @overload
    def pop(self, key: _KT, default: _VT, /) -> _VT: ...
    @overload
    def pop(self, key: _KT, default: _T, /) -> _VT | _T: ...
    def __len__(self) -> int: ...
    def __getitem__(self, key: _KT, /) -> _VT: ...
    def __setitem__(self, key: _KT, value: _VT, /) -> None: ...
    def __delitem__(self, key: _KT, /) -> None: ...
    def __iter__(self) -> Iterator[_KT]: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __reversed__(self) -> Iterator[_KT]: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...
        @overload
        def __or__(self, value: dict[_KT, _VT], /) -> dict[_KT, _VT]: ...
        @overload
        def __or__(self, value: dict[_T1, _T2], /) -> dict[_KT | _T1, _VT | _T2]: ...
        @overload
        def __ror__(self, value: dict[_KT, _VT], /) -> dict[_KT, _VT]: ...
        @overload
        def __ror__(self, value: dict[_T1, _T2], /) -> dict[_KT | _T1, _VT | _T2]: ...
        # dict.__ior__ should be kept roughly in line with MutableMapping.update()
        @overload  # type: ignore[misc]
        def __ior__(self, value: SupportsKeysAndGetItem[_KT, _VT], /) -> Self: ...
        @overload
        def __ior__(self, value: Iterable[tuple[_KT, _VT]], /) -> Self: ...

class set(MutableSet[_T]):
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, iterable: Iterable[_T], /) -> None: ...
    def add(self, element: _T, /) -> None: ...
    def copy(self) -> set[_T]: ...
    def difference(self, *s: Iterable[Any]) -> set[_T]: ...
    def difference_update(self, *s: Iterable[Any]) -> None: ...
    def discard(self, element: _T, /) -> None: ...
    def intersection(self, *s: Iterable[Any]) -> set[_T]: ...
    def intersection_update(self, *s: Iterable[Any]) -> None: ...
    def isdisjoint(self, s: Iterable[Any], /) -> bool: ...
    def issubset(self, s: Iterable[Any], /) -> bool: ...
    def issuperset(self, s: Iterable[Any], /) -> bool: ...
    def remove(self, element: _T, /) -> None: ...
    def symmetric_difference(self, s: Iterable[_T], /) -> set[_T]: ...
    def symmetric_difference_update(self, s: Iterable[_T], /) -> None: ...
    def union(self, *s: Iterable[_S]) -> set[_T | _S]: ...
    def update(self, *s: Iterable[_T]) -> None: ...
    def __len__(self) -> int: ...
    def __contains__(self, o: object, /) -> bool: ...
    def __iter__(self) -> Iterator[_T]: ...
    def __and__(self, value: AbstractSet[object], /) -> set[_T]: ...
    def __iand__(self, value: AbstractSet[object], /) -> Self: ...
    def __or__(self, value: AbstractSet[_S], /) -> set[_T | _S]: ...
    def __ior__(self, value: AbstractSet[_T], /) -> Self: ...  # type: ignore[override,misc]
    def __sub__(self, value: AbstractSet[_T | None], /) -> set[_T]: ...
    def __isub__(self, value: AbstractSet[object], /) -> Self: ...
    def __xor__(self, value: AbstractSet[_S], /) -> set[_T | _S]: ...
    def __ixor__(self, value: AbstractSet[_T], /) -> Self: ...  # type: ignore[override,misc]
    def __le__(self, value: AbstractSet[object], /) -> bool: ...
    def __lt__(self, value: AbstractSet[object], /) -> bool: ...
    def __ge__(self, value: AbstractSet[object], /) -> bool: ...
    def __gt__(self, value: AbstractSet[object], /) -> bool: ...
    def __eq__(self, value: object, /) -> bool: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

class frozenset(AbstractSet[_T_co]):
    @overload
    def __new__(cls) -> Self: ...
    @overload
    def __new__(cls, iterable: Iterable[_T_co], /) -> Self: ...
    def copy(self) -> frozenset[_T_co]: ...
    def difference(self, *s: Iterable[object]) -> frozenset[_T_co]: ...
    def intersection(self, *s: Iterable[object]) -> frozenset[_T_co]: ...
    def isdisjoint(self, s: Iterable[_T_co], /) -> bool: ...
    def issubset(self, s: Iterable[object], /) -> bool: ...
    def issuperset(self, s: Iterable[object], /) -> bool: ...
    def symmetric_difference(self, s: Iterable[_T_co], /) -> frozenset[_T_co]: ...
    def union(self, *s: Iterable[_S]) -> frozenset[_T_co | _S]: ...
    def __len__(self) -> int: ...
    def __contains__(self, o: object, /) -> bool: ...
    def __iter__(self) -> Iterator[_T_co]: ...
    def __and__(self, value: AbstractSet[_T_co], /) -> frozenset[_T_co]: ...
    def __or__(self, value: AbstractSet[_S], /) -> frozenset[_T_co | _S]: ...
    def __sub__(self, value: AbstractSet[_T_co], /) -> frozenset[_T_co]: ...
    def __xor__(self, value: AbstractSet[_S], /) -> frozenset[_T_co | _S]: ...
    def __le__(self, value: AbstractSet[object], /) -> bool: ...
    def __lt__(self, value: AbstractSet[object], /) -> bool: ...
    def __ge__(self, value: AbstractSet[object], /) -> bool: ...
    def __gt__(self, value: AbstractSet[object], /) -> bool: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __hash__(self) -> int: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

class enumerate(Iterator[tuple[int, _T]]):
    def __new__(cls, iterable: Iterable[_T], start: int = 0) -> Self: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> tuple[int, _T]: ...
    if sys.version_info >= (3, 9):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

@final
class range(Sequence[int]):
    @property
    def start(self) -> int: ...
    @property
    def stop(self) -> int: ...
    @property
    def step(self) -> int: ...
    @overload
    def __new__(cls, stop: SupportsIndex, /) -> Self: ...
    @overload
    def __new__(cls, start: SupportsIndex, stop: SupportsIndex, step: SupportsIndex = ..., /) -> Self: ...
    def count(self, value: int, /) -> int: ...
    def index(self, value: int, /) -> int: ...  # type: ignore[override]
    def __len__(self) -> int: ...
    def __eq__(self, value: object, /) -> bool: ...
    def __hash__(self) -> int: ...
    def __contains__(self, key: object, /) -> bool: ...
    def __iter__(self) -> Iterator[int]: ...
    @overload
    def __getitem__(self, key: SupportsIndex, /) -> int: ...
    @overload
    def __getitem__(self, key: slice, /) -> range: ...
    def __reversed__(self) -> Iterator[int]: ...

class property:
    fget: Callable[[Any], Any] | None
    fset: Callable[[Any, Any], None] | None
    fdel: Callable[[Any], None] | None
    __isabstractmethod__: bool
    if sys.version_info >= (3, 13):
        __name__: str

    def __init__(
        self,
        fget: Callable[[Any], Any] | None = ...,
        fset: Callable[[Any, Any], None] | None = ...,
        fdel: Callable[[Any], None] | None = ...,
        doc: str | None = ...,
    ) -> None: ...
    def getter(self, fget: Callable[[Any], Any], /) -> property: ...
    def setter(self, fset: Callable[[Any, Any], None], /) -> property: ...
    def deleter(self, fdel: Callable[[Any], None], /) -> property: ...
    def __get__(self, instance: Any, owner: type | None = None, /) -> Any: ...
    def __set__(self, instance: Any, value: Any, /) -> None: ...
    def __delete__(self, instance: Any, /) -> None: ...

@final
class _NotImplementedType(Any):
    __call__: None

NotImplemented: _NotImplementedType

def abs(x: SupportsAbs[_T], /) -> _T: ...
def all(iterable: Iterable[object], /) -> bool: ...
def any(iterable: Iterable[object], /) -> bool: ...
def ascii(obj: object, /) -> str: ...
def bin(number: int | SupportsIndex, /) -> str: ...
def breakpoint(*args: Any, **kws: Any) -> None: ...
def callable(obj: object, /) -> TypeIs[Callable[..., object]]: ...
def chr(i: int, /) -> str: ...

# We define this here instead of using os.PathLike to avoid import cycle issues.
# See https://github.com/python/typeshed/pull/991#issuecomment-288160993
class _PathLike(Protocol[AnyStr_co]):
    def __fspath__(self) -> AnyStr_co: ...

if sys.version_info >= (3, 10):
    def aiter(async_iterable: SupportsAiter[_SupportsAnextT], /) -> _SupportsAnextT: ...

    class _SupportsSynchronousAnext(Protocol[_AwaitableT_co]):
        def __anext__(self) -> _AwaitableT_co: ...

    @overload
    # `anext` is not, in fact, an async function. When default is not provided
    # `anext` is just a passthrough for `obj.__anext__`
    # See discussion in #7491 and pure-Python implementation of `anext` at https://github.com/python/cpython/blob/ea786a882b9ed4261eafabad6011bc7ef3b5bf94/Lib/test/test_asyncgen.py#L52-L80
    def anext(i: _SupportsSynchronousAnext[_AwaitableT], /) -> _AwaitableT: ...
    @overload
    async def anext(i: SupportsAnext[_T], default: _VT, /) -> _T | _VT: ...

# compile() returns a CodeType, unless the flags argument includes PyCF_ONLY_AST (=1024),
# in which case it returns ast.AST. We have overloads for flag 0 (the default) and for
# explicitly passing PyCF_ONLY_AST. We fall back to Any for other values of flags.
@overload
def compile(
    source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
    filename: str | ReadableBuffer | _PathLike[Any],
    mode: str,
    flags: Literal[0],
    dont_inherit: bool = False,
    optimize: int = -1,
    *,
    _feature_version: int = -1,
) -> CodeType: ...
@overload
def compile(
    source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
    filename: str | ReadableBuffer | _PathLike[Any],
    mode: str,
    *,
    dont_inherit: bool = False,
    optimize: int = -1,
    _feature_version: int = -1,
) -> CodeType: ...
@overload
def compile(
    source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
    filename: str | ReadableBuffer | _PathLike[Any],
    mode: str,
    flags: Literal[1024],
    dont_inherit: bool = False,
    optimize: int = -1,
    *,
    _feature_version: int = -1,
) -> _ast.AST: ...
@overload
def compile(
    source: str | ReadableBuffer | _ast.Module | _ast.Expression | _ast.Interactive,
    filename: str | ReadableBuffer | _PathLike[Any],
    mode: str,
    flags: int,
    dont_inherit: bool = False,
    optimize: int = -1,
    *,
    _feature_version: int = -1,
) -> Any: ...

copyright: _sitebuiltins._Printer
credits: _sitebuiltins._Printer

def delattr(obj: object, name: str, /) -> None: ...
def dir(o: object = ..., /) -> list[str]: ...
@overload
def divmod(x: SupportsDivMod[_T_contra, _T_co], y: _T_contra, /) -> _T_co: ...
@overload
def divmod(x: _T_contra, y: SupportsRDivMod[_T_contra, _T_co], /) -> _T_co: ...

# The `globals` argument to `eval` has to be `dict[str, Any]` rather than `dict[str, object]` due to invariance.
# (The `globals` argument has to be a "real dict", rather than any old mapping, unlike the `locals` argument.)
if sys.version_info >= (3, 13):
    def eval(
        source: str | ReadableBuffer | CodeType,
        /,
        globals: dict[str, Any] | None = None,
        locals: Mapping[str, object] | None = None,
    ) -> Any: ...

else:
    def eval(
        source: str | ReadableBuffer | CodeType,
        globals: dict[str, Any] | None = None,
        locals: Mapping[str, object] | None = None,
        /,
    ) -> Any: ...

# Comment above regarding `eval` applies to `exec` as well
if sys.version_info >= (3, 13):
    def exec(
        source: str | ReadableBuffer | CodeType,
        /,
        globals: dict[str, Any] | None = None,
        locals: Mapping[str, object] | None = None,
        *,
        closure: tuple[CellType, ...] | None = None,
    ) -> None: ...

elif sys.version_info >= (3, 11):
    def exec(
        source: str | ReadableBuffer | CodeType,
        globals: dict[str, Any] | None = None,
        locals: Mapping[str, object] | None = None,
        /,
        *,
        closure: tuple[CellType, ...] | None = None,
    ) -> None: ...

else:
    def exec(
        source: str | ReadableBuffer | CodeType,
        globals: dict[str, Any] | None = None,
        locals: Mapping[str, object] | None = None,
        /,
    ) -> None: ...

exit: _sitebuiltins.Quitter

class filter(Iterator[_T]):
    @overload
    def __new__(cls, function: None, iterable: Iterable[_T | None], /) -> Self: ...
    @overload
    def __new__(cls, function: Callable[[_S], TypeGuard[_T]], iterable: Iterable[_S], /) -> Self: ...
    @overload
    def __new__(cls, function: Callable[[_S], TypeIs[_T]], iterable: Iterable[_S], /) -> Self: ...
    @overload
    def __new__(cls, function: Callable[[_T], Any], iterable: Iterable[_T], /) -> Self: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> _T: ...

def format(value: object, format_spec: str = "", /) -> str: ...
@overload
def getattr(o: object, name: str, /) -> Any: ...

# While technically covered by the last overload, spelling out the types for None, bool
# and basic containers help mypy out in some tricky situations involving type context
# (aka bidirectional inference)
@overload
def getattr(o: object, name: str, default: None, /) -> Any | None: ...
@overload
def getattr(o: object, name: str, default: bool, /) -> Any | bool: ...
@overload
def getattr(o: object, name: str, default: list[Any], /) -> Any | list[Any]: ...
@overload
def getattr(o: object, name: str, default: dict[Any, Any], /) -> Any | dict[Any, Any]: ...
@overload
def getattr(o: object, name: str, default: _T, /) -> Any | _T: ...
def globals() -> dict[str, Any]: ...
def hasattr(obj: object, name: str, /) -> bool: ...
def hash(obj: object, /) -> int: ...

help: _sitebuiltins._Helper

def hex(number: int | SupportsIndex, /) -> str: ...
def id(obj: object, /) -> int: ...
def input(prompt: object = "", /) -> str: ...

class _GetItemIterable(Protocol[_T_co]):
    def __getitem__(self, i: int, /) -> _T_co: ...

@overload
def iter(object: SupportsIter[_SupportsNextT], /) -> _SupportsNextT: ...
@overload
def iter(object: _GetItemIterable[_T], /) -> Iterator[_T]: ...
@overload
def iter(object: Callable[[], _T | None], sentinel: None, /) -> Iterator[_T]: ...
@overload
def iter(object: Callable[[], _T], sentinel: object, /) -> Iterator[_T]: ...

# Keep this alias in sync with unittest.case._ClassInfo
if sys.version_info >= (3, 10):
    _ClassInfo: TypeAlias = type | types.UnionType | tuple[_ClassInfo, ...]
else:
    _ClassInfo: TypeAlias = type | tuple[_ClassInfo, ...]

def isinstance(obj: object, class_or_tuple: _ClassInfo, /) -> bool: ...
def issubclass(cls: type, class_or_tuple: _ClassInfo, /) -> bool: ...
def len(obj: Sized, /) -> int: ...

license: _sitebuiltins._Printer

def locals() -> dict[str, Any]: ...

class map(Iterator[_S]):
    @overload
    def __new__(cls, func: Callable[[_T1], _S], iterable: Iterable[_T1], /) -> Self: ...
    @overload
    def __new__(cls, func: Callable[[_T1, _T2], _S], iterable: Iterable[_T1], iter2: Iterable[_T2], /) -> Self: ...
    @overload
    def __new__(
        cls, func: Callable[[_T1, _T2, _T3], _S], iterable: Iterable[_T1], iter2: Iterable[_T2], iter3: Iterable[_T3], /
    ) -> Self: ...
    @overload
    def __new__(
        cls,
        func: Callable[[_T1, _T2, _T3, _T4], _S],
        iterable: Iterable[_T1],
        iter2: Iterable[_T2],
        iter3: Iterable[_T3],
        iter4: Iterable[_T4],
        /,
    ) -> Self: ...
    @overload
    def __new__(
        cls,
        func: Callable[[_T1, _T2, _T3, _T4, _T5], _S],
        iterable: Iterable[_T1],
        iter2: Iterable[_T2],
        iter3: Iterable[_T3],
        iter4: Iterable[_T4],
        iter5: Iterable[_T5],
        /,
    ) -> Self: ...
    @overload
    def __new__(
        cls,
        func: Callable[..., _S],
        iterable: Iterable[Any],
        iter2: Iterable[Any],
        iter3: Iterable[Any],
        iter4: Iterable[Any],
        iter5: Iterable[Any],
        iter6: Iterable[Any],
        /,
        *iterables: Iterable[Any],
    ) -> Self: ...
    def __iter__(self) -> Self: ...
    def __next__(self) -> _S: ...

@overload
def max(
    arg1: SupportsRichComparisonT, arg2: SupportsRichComparisonT, /, *_args: SupportsRichComparisonT, key: None = None
) -> SupportsRichComparisonT: ...
@overload
def max(arg1: _T, arg2: _T, /, *_args: _T, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def max(iterable: Iterable[SupportsRichComparisonT], /, *, key: None = None) -> SupportsRichComparisonT: ...
@overload
def max(iterable: Iterable[_T], /, *, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def max(iterable: Iterable[SupportsRichComparisonT], /, *, key: None = None, default: _T) -> SupportsRichComparisonT | _T: ...
@overload
def max(iterable: Iterable[_T1], /, *, key: Callable[[_T1], SupportsRichComparison], default: _T2) -> _T1 | _T2: ...
@overload
def min(
    arg1: SupportsRichComparisonT, arg2: SupportsRichComparisonT, /, *_args: SupportsRichComparisonT, key: None = None
) -> SupportsRichComparisonT: ...
@overload
def min(arg1: _T, arg2: _T, /, *_args: _T, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def min(iterable: Iterable[SupportsRichComparisonT], /, *, key: None = None) -> SupportsRichComparisonT: ...
@overload
def min(iterable: Iterable[_T], /, *, key: Callable[[_T], SupportsRichComparison]) -> _T: ...
@overload
def min(iterable: Iterable[SupportsRichComparisonT], /, *, key: None = None, default: _T) -> SupportsRichComparisonT | _T: ...
@overload
def min(iterable: Iterable[_T1], /, *, key: Callable[[_T1], SupportsRichComparison], default: _T2) -> _T1 | _T2: ...
@overload
def next(i: SupportsNext[_T], /) -> _T: ...
@overload
def next(i: SupportsNext[_T], default: _VT, /) -> _T | _VT: ...
def oct(number: int | SupportsIndex, /) -> str: ...

_Opener: TypeAlias = Callable[[str, int], int]

# Text mode: always returns a TextIOWrapper
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenTextMode = "r",
    buffering: int = -1,
    encoding: str | None = None,
    errors: str | None = None,
    newline: str | None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> TextIOWrapper: ...

# Unbuffered binary mode: returns a FileIO
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryMode,
    buffering: Literal[0],
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> FileIO: ...

# Buffering is on: return BufferedRandom, BufferedReader, or BufferedWriter
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryModeUpdating,
    buffering: Literal[-1, 1] = -1,
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> BufferedRandom: ...
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryModeWriting,
    buffering: Literal[-1, 1] = -1,
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> BufferedWriter: ...
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryModeReading,
    buffering: Literal[-1, 1] = -1,
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> BufferedReader: ...

# Buffering cannot be determined: fall back to BinaryIO
@overload
def open(
    file: FileDescriptorOrPath,
    mode: OpenBinaryMode,
    buffering: int = -1,
    encoding: None = None,
    errors: None = None,
    newline: None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> BinaryIO: ...

# Fallback if mode is not specified
@overload
def open(
    file: FileDescriptorOrPath,
    mode: str,
    buffering: int = -1,
    encoding: str | None = None,
    errors: str | None = None,
    newline: str | None = None,
    closefd: bool = True,
    opener: _Opener | None = None,
) -> IO[Any]: ...
def ord(c: str | bytes | bytearray, /) -> int: ...

class _SupportsWriteAndFlush(SupportsWrite[_T_contra], SupportsFlush, Protocol[_T_contra]): ...

@overload
def print(
    *values: object,
    sep: str | None = " ",
    end: str | None = "\n",
    file: SupportsWrite[str] | None = None,
    flush: Literal[False] = False,
) -> None: ...
@overload
def print(
    *values: object, sep: str | None = " ", end: str | None = "\n", file: _SupportsWriteAndFlush[str] | None = None, flush: bool
) -> None: ...

_E = TypeVar("_E", contravariant=True)
_M = TypeVar("_M", contravariant=True)

class _SupportsPow2(Protocol[_E, _T_co]):
    def __pow__(self, other: _E, /) -> _T_co: ...

class _SupportsPow3NoneOnly(Protocol[_E, _T_co]):
    def __pow__(self, other: _E, modulo: None = None, /) -> _T_co: ...

class _SupportsPow3(Protocol[_E, _M, _T_co]):
    def __pow__(self, other: _E, modulo: _M, /) -> _T_co: ...

_SupportsSomeKindOfPow = (  # noqa: Y026  # TODO: Use TypeAlias once mypy bugs are fixed
    _SupportsPow2[Any, Any] | _SupportsPow3NoneOnly[Any, Any] | _SupportsPow3[Any, Any, Any]
)

# TODO: `pow(int, int, Literal[0])` fails at runtime,
# but adding a `NoReturn` overload isn't a good solution for expressing that (see #8566).
@overload
def pow(base: int, exp: int, mod: int) -> int: ...
@overload
def pow(base: int, exp: Literal[0], mod: None = None) -> Literal[1]: ...
@overload
def pow(base: int, exp: _PositiveInteger, mod: None = None) -> int: ...
@overload
def pow(base: int, exp: _NegativeInteger, mod: None = None) -> float: ...

# int base & positive-int exp -> int; int base & negative-int exp -> float
# return type must be Any as `int | float` causes too many false-positive errors
@overload
def pow(base: int, exp: int, mod: None = None) -> Any: ...
@overload
def pow(base: _PositiveInteger, exp: float, mod: None = None) -> float: ...
@overload
def pow(base: _NegativeInteger, exp: float, mod: None = None) -> complex: ...
@overload
def pow(base: float, exp: int, mod: None = None) -> float: ...

# float base & float exp could return float or complex
# return type must be Any (same as complex base, complex exp),
# as `float | complex` causes too many false-positive errors
@overload
def pow(base: float, exp: complex | _SupportsSomeKindOfPow, mod: None = None) -> Any: ...
@overload
def pow(base: complex, exp: complex | _SupportsSomeKindOfPow, mod: None = None) -> complex: ...
@overload
def pow(base: _SupportsPow2[_E, _T_co], exp: _E, mod: None = None) -> _T_co: ...  # type: ignore[overload-overlap]
@overload
def pow(base: _SupportsPow3NoneOnly[_E, _T_co], exp: _E, mod: None = None) -> _T_co: ...  # type: ignore[overload-overlap]
@overload
def pow(base: _SupportsPow3[_E, _M, _T_co], exp: _E, mod: _M) -> _T_co: ...
@overload
def pow(base: _SupportsSomeKindOfPow, exp: float, mod: None = None) -> Any: ...
@overload
def pow(base: _SupportsSomeKindOfPow, exp: complex, mod: None = None) -> complex: ...

quit: _sitebuiltins.Quitter

class reversed(Iterator[_T]):
    @overload
    def __new__(cls, sequence: Reversible[_T], /) -> Iterator[_T]: ...  # type: ignore[misc]
    @overload
    def __new__(cls, sequence: SupportsLenAndGetItem[_T], /) -> Iterator[_T]: ...  # type: ignore[misc]
    def __iter__(self) -> Self: ...
    def __next__(self) -> _T: ...
    def __length_hint__(self) -> int: ...

def repr(obj: object, /) -> str: ...

# See https://github.com/python/typeshed/pull/9141
# and https://github.com/python/typeshed/pull/9151
# on why we don't use `SupportsRound` from `typing.pyi`

class _SupportsRound1(Protocol[_T_co]):
    def __round__(self) -> _T_co: ...

class _SupportsRound2(Protocol[_T_co]):
    def __round__(self, ndigits: int, /) -> _T_co: ...

@overload
def round(number: _SupportsRound1[_T], ndigits: None = None) -> _T: ...
@overload
def round(number: _SupportsRound2[_T], ndigits: SupportsIndex) -> _T: ...

# See https://github.com/python/typeshed/pull/6292#discussion_r748875189
# for why arg 3 of `setattr` should be annotated with `Any` and not `object`
def setattr(obj: object, name: str, value: Any, /) -> None: ...
@overload
def sorted(
    iterable: Iterable[SupportsRichComparisonT], /, *, key: None = None, reverse: bool = False
) -> list[SupportsRichComparisonT]: ...
@overload
def sorted(iterable: Iterable[_T], /, *, key: Callable[[_T], SupportsRichComparison], reverse: bool = False) -> list[_T]: ...

_AddableT1 = TypeVar("_AddableT1", bound=SupportsAdd[Any, Any])
_AddableT2 = TypeVar("_AddableT2", bound=SupportsAdd[Any, Any])

class _SupportsSumWithNoDefaultGiven(SupportsAdd[Any, Any], SupportsRAdd[int, Any], Protocol): ...

_SupportsSumNoDefaultT = TypeVar("_SupportsSumNoDefaultT", bound=_SupportsSumWithNoDefaultGiven)

# In general, the return type of `x + x` is *not* guaranteed to be the same type as x.
# However, we can't express that in the stub for `sum()`
# without creating many false-positive errors (see #7578).
# Instead, we special-case the most common examples of this: bool and literal integers.
@overload
def sum(iterable: Iterable[bool], /, start: int = 0) -> int: ...
@overload
def sum(iterable: Iterable[_SupportsSumNoDefaultT], /) -> _SupportsSumNoDefaultT | Literal[0]: ...
@overload
def sum(iterable: Iterable[_AddableT1], /, start: _AddableT2) -> _AddableT1 | _AddableT2: ...

# The argument to `vars()` has to have a `__dict__` attribute, so the second overload can't be annotated with `object`
# (A "SupportsDunderDict" protocol doesn't work)
@overload
def vars(object: type, /) -> types.MappingProxyType[str, Any]: ...
@overload
def vars(object: Any = ..., /) -> dict[str, Any]: ...

class zip(Iterator[_T_co]):
    if sys.version_info >= (3, 10):
        @overload
        def __new__(cls, *, strict: bool = ...) -> zip[Any]: ...
        @overload
        def __new__(cls, iter1: Iterable[_T1], /, *, strict: bool = ...) -> zip[tuple[_T1]]: ...
        @overload
        def __new__(cls, iter1: Iterable[_T1], iter2: Iterable[_T2], /, *, strict: bool = ...) -> zip[tuple[_T1, _T2]]: ...
        @overload
        def __new__(
            cls, iter1: Iterable[_T1], iter2: Iterable[_T2], iter3: Iterable[_T3], /, *, strict: bool = ...
        ) -> zip[tuple[_T1, _T2, _T3]]: ...
        @overload
        def __new__(
            cls, iter1: Iterable[_T1], iter2: Iterable[_T2], iter3: Iterable[_T3], iter4: Iterable[_T4], /, *, strict: bool = ...
        ) -> zip[tuple[_T1, _T2, _T3, _T4]]: ...
        @overload
        def __new__(
            cls,
            iter1: Iterable[_T1],
            iter2: Iterable[_T2],
            iter3: Iterable[_T3],
            iter4: Iterable[_T4],
            iter5: Iterable[_T5],
            /,
            *,
            strict: bool = ...,
        ) -> zip[tuple[_T1, _T2, _T3, _T4, _T5]]: ...
        @overload
        def __new__(
            cls,
            iter1: Iterable[Any],
            iter2: Iterable[Any],
            iter3: Iterable[Any],
            iter4: Iterable[Any],
            iter5: Iterable[Any],
            iter6: Iterable[Any],
            /,
            *iterables: Iterable[Any],
            strict: bool = ...,
        ) -> zip[tuple[Any, ...]]: ...
    else:
        @overload
        def __new__(cls) -> zip[Any]: ...
        @overload
        def __new__(cls, iter1: Iterable[_T1], /) -> zip[tuple[_T1]]: ...
        @overload
        def __new__(cls, iter1: Iterable[_T1], iter2: Iterable[_T2], /) -> zip[tuple[_T1, _T2]]: ...
        @overload
        def __new__(cls, iter1: Iterable[_T1], iter2: Iterable[_T2], iter3: Iterable[_T3], /) -> zip[tuple[_T1, _T2, _T3]]: ...
        @overload
        def __new__(
            cls, iter1: Iterable[_T1], iter2: Iterable[_T2], iter3: Iterable[_T3], iter4: Iterable[_T4], /
        ) -> zip[tuple[_T1, _T2, _T3, _T4]]: ...
        @overload
        def __new__(
            cls, iter1: Iterable[_T1], iter2: Iterable[_T2], iter3: Iterable[_T3], iter4: Iterable[_T4], iter5: Iterable[_T5], /
        ) -> zip[tuple[_T1, _T2, _T3, _T4, _T5]]: ...
        @overload
        def __new__(
            cls,
            iter1: Iterable[Any],
            iter2: Iterable[Any],
            iter3: Iterable[Any],
            iter4: Iterable[Any],
            iter5: Iterable[Any],
            iter6: Iterable[Any],
            /,
            *iterables: Iterable[Any],
        ) -> zip[tuple[Any, ...]]: ...

    def __iter__(self) -> Self: ...
    def __next__(self) -> _T_co: ...

# Signature of `builtins.__import__` should be kept identical to `importlib.__import__`
# Return type of `__import__` should be kept the same as return type of `importlib.import_module`
def __import__(
    name: str,
    globals: Mapping[str, object] | None = None,
    locals: Mapping[str, object] | None = None,
    fromlist: Sequence[str] = (),
    level: int = 0,
) -> types.ModuleType: ...
def __build_class__(func: Callable[[], CellType | Any], name: str, /, *bases: Any, metaclass: Any = ..., **kwds: Any) -> Any: ...

if sys.version_info >= (3, 10):
    from types import EllipsisType

    # Backwards compatibility hack for folks who relied on the ellipsis type
    # existing in typeshed in Python 3.9 and earlier.
    ellipsis = EllipsisType

    Ellipsis: EllipsisType

else:
    # Actually the type of Ellipsis is <type 'ellipsis'>, but since it's
    # not exposed anywhere under that name, we make it private here.
    @final
    @type_check_only
    class ellipsis: ...

    Ellipsis: ellipsis

class BaseException:
    args: tuple[Any, ...]
    __cause__: BaseException | None
    __context__: BaseException | None
    __suppress_context__: bool
    __traceback__: TracebackType | None
    def __init__(self, *args: object) -> None: ...
    def __new__(cls, *args: Any, **kwds: Any) -> Self: ...
    def __setstate__(self, state: dict[str, Any] | None, /) -> None: ...
    def with_traceback(self, tb: TracebackType | None, /) -> Self: ...
    if sys.version_info >= (3, 11):
        # only present after add_note() is called
        __notes__: list[str]
        def add_note(self, note: str, /) -> None: ...

class GeneratorExit(BaseException): ...
class KeyboardInterrupt(BaseException): ...

class SystemExit(BaseException):
    code: sys._ExitCode

class Exception(BaseException): ...

class StopIteration(Exception):
    value: Any

class OSError(Exception):
    errno: int | None
    strerror: str | None
    # filename, filename2 are actually str | bytes | None
    filename: Any
    filename2: Any
    if sys.platform == "win32":
        winerror: int

EnvironmentError = OSError
IOError = OSError
if sys.platform == "win32":
    WindowsError = OSError

class ArithmeticError(Exception): ...
class AssertionError(Exception): ...

class AttributeError(Exception):
    if sys.version_info >= (3, 10):
        def __init__(self, *args: object, name: str | None = ..., obj: object = ...) -> None: ...
        name: str
        obj: object

class BufferError(Exception): ...
class EOFError(Exception): ...

class ImportError(Exception):
    def __init__(self, *args: object, name: str | None = ..., path: str | None = ...) -> None: ...
    name: str | None
    path: str | None
    msg: str  # undocumented
    if sys.version_info >= (3, 12):
        name_from: str | None  # undocumented

class LookupError(Exception): ...
class MemoryError(Exception): ...

class NameError(Exception):
    if sys.version_info >= (3, 10):
        def __init__(self, *args: object, name: str | None = ...) -> None: ...
        name: str

class ReferenceError(Exception): ...
class RuntimeError(Exception): ...
class StopAsyncIteration(Exception): ...

class SyntaxError(Exception):
    msg: str
    filename: str | None
    lineno: int | None
    offset: int | None
    text: str | None
    # Errors are displayed differently if this attribute exists on the exception.
    # The value is always None.
    print_file_and_line: None
    if sys.version_info >= (3, 10):
        end_lineno: int | None
        end_offset: int | None

    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, msg: object, /) -> None: ...
    # Second argument is the tuple (filename, lineno, offset, text)
    @overload
    def __init__(self, msg: str, info: tuple[str | None, int | None, int | None, str | None], /) -> None: ...
    if sys.version_info >= (3, 10):
        # end_lineno and end_offset must both be provided if one is.
        @overload
        def __init__(
            self, msg: str, info: tuple[str | None, int | None, int | None, str | None, int | None, int | None], /
        ) -> None: ...
    # If you provide more than two arguments, it still creates the SyntaxError, but
    # the arguments from the info tuple are not parsed. This form is omitted.

class SystemError(Exception): ...
class TypeError(Exception): ...
class ValueError(Exception): ...
class FloatingPointError(ArithmeticError): ...
class OverflowError(ArithmeticError): ...
class ZeroDivisionError(ArithmeticError): ...
class ModuleNotFoundError(ImportError): ...
class IndexError(LookupError): ...
class KeyError(LookupError): ...
class UnboundLocalError(NameError): ...

class BlockingIOError(OSError):
    characters_written: int

class ChildProcessError(OSError): ...
class ConnectionError(OSError): ...
class BrokenPipeError(ConnectionError): ...
class ConnectionAbortedError(ConnectionError): ...
class ConnectionRefusedError(ConnectionError): ...
class ConnectionResetError(ConnectionError): ...
class FileExistsError(OSError): ...
class FileNotFoundError(OSError): ...
class InterruptedError(OSError): ...
class IsADirectoryError(OSError): ...
class NotADirectoryError(OSError): ...
class PermissionError(OSError): ...
class ProcessLookupError(OSError): ...
class TimeoutError(OSError): ...
class NotImplementedError(RuntimeError): ...
class RecursionError(RuntimeError): ...
class IndentationError(SyntaxError): ...
class TabError(IndentationError): ...
class UnicodeError(ValueError): ...

class UnicodeDecodeError(UnicodeError):
    encoding: str
    object: bytes
    start: int
    end: int
    reason: str
    def __init__(self, encoding: str, object: ReadableBuffer, start: int, end: int, reason: str, /) -> None: ...

class UnicodeEncodeError(UnicodeError):
    encoding: str
    object: str
    start: int
    end: int
    reason: str
    def __init__(self, encoding: str, object: str, start: int, end: int, reason: str, /) -> None: ...

class UnicodeTranslateError(UnicodeError):
    encoding: None
    object: str
    start: int
    end: int
    reason: str
    def __init__(self, object: str, start: int, end: int, reason: str, /) -> None: ...

class Warning(Exception): ...
class UserWarning(Warning): ...
class DeprecationWarning(Warning): ...
class SyntaxWarning(Warning): ...
class RuntimeWarning(Warning): ...
class FutureWarning(Warning): ...
class PendingDeprecationWarning(Warning): ...
class ImportWarning(Warning): ...
class UnicodeWarning(Warning): ...
class BytesWarning(Warning): ...
class ResourceWarning(Warning): ...

if sys.version_info >= (3, 10):
    class EncodingWarning(Warning): ...

if sys.version_info >= (3, 11):
    _BaseExceptionT_co = TypeVar("_BaseExceptionT_co", bound=BaseException, covariant=True, default=BaseException)
    _BaseExceptionT = TypeVar("_BaseExceptionT", bound=BaseException)
    _ExceptionT_co = TypeVar("_ExceptionT_co", bound=Exception, covariant=True, default=Exception)
    _ExceptionT = TypeVar("_ExceptionT", bound=Exception)

    # See `check_exception_group.py` for use-cases and comments.
    class BaseExceptionGroup(BaseException, Generic[_BaseExceptionT_co]):
        def __new__(cls, message: str, exceptions: Sequence[_BaseExceptionT_co], /) -> Self: ...
        def __init__(self, message: str, exceptions: Sequence[_BaseExceptionT_co], /) -> None: ...
        @property
        def message(self) -> str: ...
        @property
        def exceptions(self) -> tuple[_BaseExceptionT_co | BaseExceptionGroup[_BaseExceptionT_co], ...]: ...
        @overload
        def subgroup(
            self, condition: type[_ExceptionT] | tuple[type[_ExceptionT], ...], /
        ) -> ExceptionGroup[_ExceptionT] | None: ...
        @overload
        def subgroup(
            self, condition: type[_BaseExceptionT] | tuple[type[_BaseExceptionT], ...], /
        ) -> BaseExceptionGroup[_BaseExceptionT] | None: ...
        @overload
        def subgroup(
            self, condition: Callable[[_BaseExceptionT_co | Self], bool], /
        ) -> BaseExceptionGroup[_BaseExceptionT_co] | None: ...
        @overload
        def split(
            self, condition: type[_ExceptionT] | tuple[type[_ExceptionT], ...], /
        ) -> tuple[ExceptionGroup[_ExceptionT] | None, BaseExceptionGroup[_BaseExceptionT_co] | None]: ...
        @overload
        def split(
            self, condition: type[_BaseExceptionT] | tuple[type[_BaseExceptionT], ...], /
        ) -> tuple[BaseExceptionGroup[_BaseExceptionT] | None, BaseExceptionGroup[_BaseExceptionT_co] | None]: ...
        @overload
        def split(
            self, condition: Callable[[_BaseExceptionT_co | Self], bool], /
        ) -> tuple[BaseExceptionGroup[_BaseExceptionT_co] | None, BaseExceptionGroup[_BaseExceptionT_co] | None]: ...
        # In reality it is `NonEmptySequence`:
        @overload
        def derive(self, excs: Sequence[_ExceptionT], /) -> ExceptionGroup[_ExceptionT]: ...
        @overload
        def derive(self, excs: Sequence[_BaseExceptionT], /) -> BaseExceptionGroup[_BaseExceptionT]: ...
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

    class ExceptionGroup(BaseExceptionGroup[_ExceptionT_co], Exception):
        def __new__(cls, message: str, exceptions: Sequence[_ExceptionT_co], /) -> Self: ...
        def __init__(self, message: str, exceptions: Sequence[_ExceptionT_co], /) -> None: ...
        @property
        def exceptions(self) -> tuple[_ExceptionT_co | ExceptionGroup[_ExceptionT_co], ...]: ...
        # We accept a narrower type, but that's OK.
        @overload  # type: ignore[override]
        def subgroup(
            self, condition: type[_ExceptionT] | tuple[type[_ExceptionT], ...], /
        ) -> ExceptionGroup[_ExceptionT] | None: ...
        @overload
        def subgroup(self, condition: Callable[[_ExceptionT_co | Self], bool], /) -> ExceptionGroup[_ExceptionT_co] | None: ...
        @overload  # type: ignore[override]
        def split(
            self, condition: type[_ExceptionT] | tuple[type[_ExceptionT], ...], /
        ) -> tuple[ExceptionGroup[_ExceptionT] | None, ExceptionGroup[_ExceptionT_co] | None]: ...
        @overload
        def split(
            self, condition: Callable[[_ExceptionT_co | Self], bool], /
        ) -> tuple[ExceptionGroup[_ExceptionT_co] | None, ExceptionGroup[_ExceptionT_co] | None]: ...

if sys.version_info >= (3, 13):
    class PythonFinalizationError(RuntimeError): ...
