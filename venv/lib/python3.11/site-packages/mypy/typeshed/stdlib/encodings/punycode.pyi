import codecs
from typing import Literal

def segregate(str: str) -> tuple[bytes, list[int]]: ...
def selective_len(str: str, max: int) -> int: ...
def selective_find(str: str, char: str, index: int, pos: int) -> tuple[int, int]: ...
def insertion_unsort(str: str, extended: list[int]) -> list[int]: ...
def T(j: int, bias: int) -> int: ...

digits: Literal[b"abcdefghijklmnopqrstuvwxyz0123456789"]

def generate_generalized_integer(N: int, bias: int) -> bytes: ...
def adapt(delta: int, first: bool, numchars: int) -> int: ...
def generate_integers(baselen: int, deltas: list[int]) -> bytes: ...
def punycode_encode(text: str) -> bytes: ...
def decode_generalized_number(extended: bytes, extpos: int, bias: int, errors: str) -> tuple[int, int | None]: ...
def insertion_sort(base: str, extended: bytes, errors: str) -> str: ...
def punycode_decode(text: memoryview | bytes | bytearray | str, errors: str) -> str: ...

class Codec(codecs.Codec):
    def encode(self, input: str, errors: str = "strict") -> tuple[bytes, int]: ...
    def decode(self, input: memoryview | bytes | bytearray | str, errors: str = "strict") -> tuple[str, int]: ...

class IncrementalEncoder(codecs.IncrementalEncoder):
    def encode(self, input: str, final: bool = False) -> bytes: ...

class IncrementalDecoder(codecs.IncrementalDecoder):
    def decode(self, input: memoryview | bytes | bytearray | str, final: bool = False) -> str: ...  # type: ignore[override]

class StreamWriter(Codec, codecs.StreamWriter): ...
class StreamReader(Codec, codecs.StreamReader): ...

def getregentry() -> codecs.CodecInfo: ...
