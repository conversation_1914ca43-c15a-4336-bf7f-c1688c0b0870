Metadata-Version: 2.1
Name: pandas
Version: 2.2.3
Summary: Powerful data structures for data analysis, time series, and statistics
Home-page: https://pandas.pydata.org
Author-Email: The Pandas Development Team <<EMAIL>>
License: BSD 3-Clause License
        
        Copyright (c) 2008-2011, AQR Capital Management, LLC, Lambda Foundry, Inc. and PyData Development Team
        All rights reserved.
        
        Copyright (c) 2011-2023, Open source contributors.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        * Redistributions of source code must retain the above copyright notice, this
          list of conditions and the following disclaimer.
        
        * Redistributions in binary form must reproduce the above copyright notice,
          this list of conditions and the following disclaimer in the documentation
          and/or other materials provided with the distribution.
        
        * Neither the name of the copyright holder nor the names of its
          contributors may be used to endorse or promote products derived from
          this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
        Copyright (c) 2010-2019 Keith Goodman
        Copyright (c) 2019 Bottleneck Developers
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
            * Redistributions of source code must retain the above copyright notice,
              this list of conditions and the following disclaimer.
        
            * Redistributions in binary form must reproduce the above copyright
              notice, this list of conditions and the following disclaimer in the
              documentation and/or other materials provided with the distribution.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
        ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
        LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
        CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
        SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
        INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
        CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
        ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
        POSSIBILITY OF SUCH DAMAGE.Copyright 2017- Paul Ganssle <<EMAIL>>
        Copyright 2017- dateutil contributors (see AUTHORS file)
        
           Licensed under the Apache License, Version 2.0 (the "License");
           you may not use this file except in compliance with the License.
           You may obtain a copy of the License at
        
               http://www.apache.org/licenses/LICENSE-2.0
        
           Unless required by applicable law or agreed to in writing, software
           distributed under the License is distributed on an "AS IS" BASIS,
           WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
           See the License for the specific language governing permissions and
           limitations under the License.
        
        The above license applies to all contributions after 2017-12-01, as well as
        all contributions that have been re-licensed (see AUTHORS file for the list of
        contributors who have re-licensed their code).
        --------------------------------------------------------------------------------
        dateutil - Extensions to the standard Python datetime module.
        
        Copyright (c) 2003-2011 - Gustavo Niemeyer <<EMAIL>>
        Copyright (c) 2012-2014 - Tomi Pieviläinen <<EMAIL>>
        Copyright (c) 2014-2016 - Yaron de Leeuw <<EMAIL>>
        Copyright (c) 2015-     - Paul Ganssle <<EMAIL>>
        Copyright (c) 2015-     - dateutil contributors (see AUTHORS file)
        
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
            * Redistributions of source code must retain the above copyright notice,
              this list of conditions and the following disclaimer.
            * Redistributions in binary form must reproduce the above copyright notice,
              this list of conditions and the following disclaimer in the documentation
              and/or other materials provided with the distribution.
            * Neither the name of the copyright holder nor the names of its
              contributors may be used to endorse or promote products derived from
              this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
        "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
        LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
        A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
        CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
        EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
        PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
        LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
        NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
        SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
        
        The above BSD License Applies to all code, even that also covered by Apache 2.0.# MIT License
        
        Copyright (c) 2019 Hadley Wickham; RStudio; and Evan Miller
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
        Based on http://opensource.org/licenses/MIT
        
        This is a template. Complete and ship as file LICENSE the following 2
        lines (only)
        
        YEAR:
        COPYRIGHT HOLDER:
        
        and specify as
        
        License: MIT + file LICENSE
        
        Copyright (c) <YEAR>, <COPYRIGHT HOLDER>
        
        Permission is hereby granted, free of charge, to any person obtaining
        a copy of this software and associated documentation files (the
        "Software"), to deal in the Software without restriction, including
        without limitation the rights to use, copy, modify, merge, publish,
        distribute, sublicense, and/or sell copies of the Software, and to
        permit persons to whom the Software is furnished to do so, subject to
        the following conditions:
        
        The above copyright notice and this permission notice shall be
        included in all copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
        LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
        OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
        WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
        The MIT License
        
        Copyright (c) 2008-     Attractive Chaos <<EMAIL>>
        
        Permission is hereby granted, free of charge, to any person obtaining
        a copy of this software and associated documentation files (the
        "Software"), to deal in the Software without restriction, including
        without limitation the rights to use, copy, modify, merge, publish,
        distribute, sublicense, and/or sell copies of the Software, and to
        permit persons to whom the Software is furnished to do so, subject to
        the following conditions:
        
        The above copyright notice and this permission notice shall be
        included in all copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.musl as a whole is licensed under the following standard MIT license:
        
        ----------------------------------------------------------------------
        Copyright © 2005-2020 Rich Felker, et al.
        
        Permission is hereby granted, free of charge, to any person obtaining
        a copy of this software and associated documentation files (the
        "Software"), to deal in the Software without restriction, including
        without limitation the rights to use, copy, modify, merge, publish,
        distribute, sublicense, and/or sell copies of the Software, and to
        permit persons to whom the Software is furnished to do so, subject to
        the following conditions:
        
        The above copyright notice and this permission notice shall be
        included in all copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
        IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
        CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
        TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
        SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
        ----------------------------------------------------------------------
        
        Authors/contributors include:
        
        A. Wilcox
        Ada Worcester
        Alex Dowad
        Alex Suykov
        Alexander Monakov
        Andre McCurdy
        Andrew Kelley
        Anthony G. Basile
        Aric Belsito
        Arvid Picciani
        Bartosz Brachaczek
        Benjamin Peterson
        Bobby Bingham
        Boris Brezillon
        Brent Cook
        Chris Spiegel
        Clément Vasseur
        Daniel Micay
        Daniel Sabogal
        Daurnimator
        David Carlier
        David Edelsohn
        Denys Vlasenko
        Dmitry Ivanov
        Dmitry V. Levin
        Drew DeVault
        Emil Renner Berthing
        Fangrui Song
        Felix Fietkau
        Felix Janda
        Gianluca Anzolin
        Hauke Mehrtens
        He X
        Hiltjo Posthuma
        Isaac Dunham
        Jaydeep Patil
        Jens Gustedt
        Jeremy Huntwork
        Jo-Philipp Wich
        Joakim Sindholt
        John Spencer
        Julien Ramseier
        Justin Cormack
        Kaarle Ritvanen
        Khem Raj
        Kylie McClain
        Leah Neukirchen
        Luca Barbato
        Luka Perkov
        M Farkas-Dyck (Strake)
        Mahesh Bodapati
        Markus Wichmann
        Masanori Ogino
        Michael Clark
        Michael Forney
        Mikhail Kremnyov
        Natanael Copa
        Nicholas J. Kain
        orc
        Pascal Cuoq
        Patrick Oppenlander
        Petr Hosek
        Petr Skocik
        Pierre Carrier
        Reini Urban
        Rich Felker
        Richard Pennington
        Ryan Fairfax
        Samuel Holland
        Segev Finer
        Shiz
        sin
        Solar Designer
        Stefan Kristiansson
        Stefan O'Rear
        Szabolcs Nagy
        Timo Teräs
        Trutz Behn
        Valentin Ochs
        Will Dietz
        William Haddon
        William Pitcock
        
        Portions of this software are derived from third-party works licensed
        under terms compatible with the above MIT license:
        
        The TRE regular expression implementation (src/regex/reg* and
        src/regex/tre*) is Copyright © 2001-2008 Ville Laurikari and licensed
        under a 2-clause BSD license (license text in the source files). The
        included version has been heavily modified by Rich Felker in 2012, in
        the interests of size, simplicity, and namespace cleanliness.
        
        Much of the math library code (src/math/* and src/complex/*) is
        Copyright © 1993,2004 Sun Microsystems or
        Copyright © 2003-2011 David Schultz or
        Copyright © 2003-2009 Steven G. Kargl or
        Copyright © 2003-2009 Bruce D. Evans or
        Copyright © 2008 Stephen L. Moshier or
        Copyright © 2017-2018 Arm Limited
        and labelled as such in comments in the individual source files. All
        have been licensed under extremely permissive terms.
        
        The ARM memcpy code (src/string/arm/memcpy.S) is Copyright © 2008
        The Android Open Source Project and is licensed under a two-clause BSD
        license. It was taken from Bionic libc, used on Android.
        
        The AArch64 memcpy and memset code (src/string/aarch64/*) are
        Copyright © 1999-2019, Arm Limited.
        
        The implementation of DES for crypt (src/crypt/crypt_des.c) is
        Copyright © 1994 David Burren. It is licensed under a BSD license.
        
        The implementation of blowfish crypt (src/crypt/crypt_blowfish.c) was
        originally written by Solar Designer and placed into the public
        domain. The code also comes with a fallback permissive license for use
        in jurisdictions that may not recognize the public domain.
        
        The smoothsort implementation (src/stdlib/qsort.c) is Copyright © 2011
        Valentin Ochs and is licensed under an MIT-style license.
        
        The x86_64 port was written by Nicholas J. Kain and is licensed under
        the standard MIT terms.
        
        The mips and microblaze ports were originally written by Richard
        Pennington for use in the ellcc project. The original code was adapted
        by Rich Felker for build system and code conventions during upstream
        integration. It is licensed under the standard MIT terms.
        
        The mips64 port was contributed by Imagination Technologies and is
        licensed under the standard MIT terms.
        
        The powerpc port was also originally written by Richard Pennington,
        and later supplemented and integrated by John Spencer. It is licensed
        under the standard MIT terms.
        
        All other files which have no copyright comments are original works
        produced specifically for use as part of this library, written either
        by Rich Felker, the main author of the library, or by one or more
        contibutors listed above. Details on authorship of individual files
        can be found in the git version control history of the project. The
        omission of copyright and license comments in each file is in the
        interest of source tree size.
        
        In addition, permission is hereby granted for all public header files
        (include/* and arch/*/bits/*) and crt files intended to be linked into
        applications (crt/*, ldso/dlstart.c, and arch/*/crt_arch.h) to omit
        the copyright notice and permission notice otherwise required by the
        license, and to use these files without any requirement of
        attribution. These files include substantial contributions from:
        
        Bobby Bingham
        John Spencer
        Nicholas J. Kain
        Rich Felker
        Richard Pennington
        Stefan Kristiansson
        Szabolcs Nagy
        
        all of whom have explicitly granted such permission.
        
        This file previously contained text expressing a belief that most of
        the files covered by the above exception were sufficiently trivial not
        to be subject to copyright, resulting in confusion over whether it
        negated the permissions granted in the license. In the spirit of
        permissive licensing, and of not having licensing issues being an
        obstacle to adoption, that text has been removed.Copyright (c) 2005-2023, NumPy Developers.
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are
        met:
        
            * Redistributions of source code must retain the above copyright
               notice, this list of conditions and the following disclaimer.
        
            * Redistributions in binary form must reproduce the above
               copyright notice, this list of conditions and the following
               disclaimer in the documentation and/or other materials provided
               with the distribution.
        
            * Neither the name of the NumPy Developers nor the names of any
               contributors may be used to endorse or promote products derived
               from this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
        "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
        LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
        A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
        OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
        SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
        LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
        DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
        THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
        (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
                                         Apache License
                                   Version 2.0, January 2004
                                http://www.apache.org/licenses/
        
           TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION
        
           1. Definitions.
        
              "License" shall mean the terms and conditions for use, reproduction,
              and distribution as defined by Sections 1 through 9 of this document.
        
              "Licensor" shall mean the copyright owner or entity authorized by
              the copyright owner that is granting the License.
        
              "Legal Entity" shall mean the union of the acting entity and all
              other entities that control, are controlled by, or are under common
              control with that entity. For the purposes of this definition,
              "control" means (i) the power, direct or indirect, to cause the
              direction or management of such entity, whether by contract or
              otherwise, or (ii) ownership of fifty percent (50%) or more of the
              outstanding shares, or (iii) beneficial ownership of such entity.
        
              "You" (or "Your") shall mean an individual or Legal Entity
              exercising permissions granted by this License.
        
              "Source" form shall mean the preferred form for making modifications,
              including but not limited to software source code, documentation
              source, and configuration files.
        
              "Object" form shall mean any form resulting from mechanical
              transformation or translation of a Source form, including but
              not limited to compiled object code, generated documentation,
              and conversions to other media types.
        
              "Work" shall mean the work of authorship, whether in Source or
              Object form, made available under the License, as indicated by a
              copyright notice that is included in or attached to the work
              (an example is provided in the Appendix below).
        
              "Derivative Works" shall mean any work, whether in Source or Object
              form, that is based on (or derived from) the Work and for which the
              editorial revisions, annotations, elaborations, or other modifications
              represent, as a whole, an original work of authorship. For the purposes
              of this License, Derivative Works shall not include works that remain
              separable from, or merely link (or bind by name) to the interfaces of,
              the Work and Derivative Works thereof.
        
              "Contribution" shall mean any work of authorship, including
              the original version of the Work and any modifications or additions
              to that Work or Derivative Works thereof, that is intentionally
              submitted to Licensor for inclusion in the Work by the copyright owner
              or by an individual or Legal Entity authorized to submit on behalf of
              the copyright owner. For the purposes of this definition, "submitted"
              means any form of electronic, verbal, or written communication sent
              to the Licensor or its representatives, including but not limited to
              communication on electronic mailing lists, source code control systems,
              and issue tracking systems that are managed by, or on behalf of, the
              Licensor for the purpose of discussing and improving the Work, but
              excluding communication that is conspicuously marked or otherwise
              designated in writing by the copyright owner as "Not a Contribution."
        
              "Contributor" shall mean Licensor and any individual or Legal Entity
              on behalf of whom a Contribution has been received by Licensor and
              subsequently incorporated within the Work.
        
           2. Grant of Copyright License. Subject to the terms and conditions of
              this License, each Contributor hereby grants to You a perpetual,
              worldwide, non-exclusive, no-charge, royalty-free, irrevocable
              copyright license to reproduce, prepare Derivative Works of,
              publicly display, publicly perform, sublicense, and distribute the
              Work and such Derivative Works in Source or Object form.
        
           3. Grant of Patent License. Subject to the terms and conditions of
              this License, each Contributor hereby grants to You a perpetual,
              worldwide, non-exclusive, no-charge, royalty-free, irrevocable
              (except as stated in this section) patent license to make, have made,
              use, offer to sell, sell, import, and otherwise transfer the Work,
              where such license applies only to those patent claims licensable
              by such Contributor that are necessarily infringed by their
              Contribution(s) alone or by combination of their Contribution(s)
              with the Work to which such Contribution(s) was submitted. If You
              institute patent litigation against any entity (including a
              cross-claim or counterclaim in a lawsuit) alleging that the Work
              or a Contribution incorporated within the Work constitutes direct
              or contributory patent infringement, then any patent licenses
              granted to You under this License for that Work shall terminate
              as of the date such litigation is filed.
        
           4. Redistribution. You may reproduce and distribute copies of the
              Work or Derivative Works thereof in any medium, with or without
              modifications, and in Source or Object form, provided that You
              meet the following conditions:
        
              (a) You must give any other recipients of the Work or
                  Derivative Works a copy of this License; and
        
              (b) You must cause any modified files to carry prominent notices
                  stating that You changed the files; and
        
              (c) You must retain, in the Source form of any Derivative Works
                  that You distribute, all copyright, patent, trademark, and
                  attribution notices from the Source form of the Work,
                  excluding those notices that do not pertain to any part of
                  the Derivative Works; and
        
              (d) If the Work includes a "NOTICE" text file as part of its
                  distribution, then any Derivative Works that You distribute must
                  include a readable copy of the attribution notices contained
                  within such NOTICE file, excluding those notices that do not
                  pertain to any part of the Derivative Works, in at least one
                  of the following places: within a NOTICE text file distributed
                  as part of the Derivative Works; within the Source form or
                  documentation, if provided along with the Derivative Works; or,
                  within a display generated by the Derivative Works, if and
                  wherever such third-party notices normally appear. The contents
                  of the NOTICE file are for informational purposes only and
                  do not modify the License. You may add Your own attribution
                  notices within Derivative Works that You distribute, alongside
                  or as an addendum to the NOTICE text from the Work, provided
                  that such additional attribution notices cannot be construed
                  as modifying the License.
        
              You may add Your own copyright statement to Your modifications and
              may provide additional or different license terms and conditions
              for use, reproduction, or distribution of Your modifications, or
              for any such Derivative Works as a whole, provided Your use,
              reproduction, and distribution of the Work otherwise complies with
              the conditions stated in this License.
        
           5. Submission of Contributions. Unless You explicitly state otherwise,
              any Contribution intentionally submitted for inclusion in the Work
              by You to the Licensor shall be under the terms and conditions of
              this License, without any additional terms or conditions.
              Notwithstanding the above, nothing herein shall supersede or modify
              the terms of any separate license agreement you may have executed
              with Licensor regarding such Contributions.
        
           6. Trademarks. This License does not grant permission to use the trade
              names, trademarks, service marks, or product names of the Licensor,
              except as required for reasonable and customary use in describing the
              origin of the Work and reproducing the content of the NOTICE file.
        
           7. Disclaimer of Warranty. Unless required by applicable law or
              agreed to in writing, Licensor provides the Work (and each
              Contributor provides its Contributions) on an "AS IS" BASIS,
              WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
              implied, including, without limitation, any warranties or conditions
              of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
              PARTICULAR PURPOSE. You are solely responsible for determining the
              appropriateness of using or redistributing the Work and assume any
              risks associated with Your exercise of permissions under this License.
        
           8. Limitation of Liability. In no event and under no legal theory,
              whether in tort (including negligence), contract, or otherwise,
              unless required by applicable law (such as deliberate and grossly
              negligent acts) or agreed to in writing, shall any Contributor be
              liable to You for damages, including any direct, indirect, special,
              incidental, or consequential damages of any character arising as a
              result of this License or out of the use or inability to use the
              Work (including but not limited to damages for loss of goodwill,
              work stoppage, computer failure or malfunction, or any and all
              other commercial damages or losses), even if such Contributor
              has been advised of the possibility of such damages.
        
           9. Accepting Warranty or Additional Liability. While redistributing
              the Work or Derivative Works thereof, You may choose to offer,
              and charge a fee for, acceptance of support, warranty, indemnity,
              or other liability obligations and/or rights consistent with this
              License. However, in accepting such obligations, You may act only
              on Your own behalf and on Your sole responsibility, not on behalf
              of any other Contributor, and only if You agree to indemnify,
              defend, and hold each Contributor harmless for any liability
              incurred by, or claims asserted against, such Contributor by reason
              of your accepting any such warranty or additional liability.
        
           END OF TERMS AND CONDITIONS
        
        
        Copyright (c) Donald Stufft and individual contributors.
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
            1. Redistributions of source code must retain the above copyright notice,
               this list of conditions and the following disclaimer.
        
            2. Redistributions in binary form must reproduce the above copyright
               notice, this list of conditions and the following disclaimer in the
               documentation and/or other materials provided with the distribution.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
        ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
        WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.A. HISTORY OF THE SOFTWARE
        ==========================
        
        Python was created in the early 1990s by Guido van Rossum at Stichting
        Mathematisch Centrum (CWI, see https://www.cwi.nl) in the Netherlands
        as a successor of a language called ABC.  Guido remains Python's
        principal author, although it includes many contributions from others.
        
        In 1995, Guido continued his work on Python at the Corporation for
        National Research Initiatives (CNRI, see https://www.cnri.reston.va.us)
        in Reston, Virginia where he released several versions of the
        software.
        
        In May 2000, Guido and the Python core development team moved to
        BeOpen.com to form the BeOpen PythonLabs team.  In October of the same
        year, the PythonLabs team moved to Digital Creations, which became
        Zope Corporation.  In 2001, the Python Software Foundation (PSF, see
        https://www.python.org/psf/) was formed, a non-profit organization
        created specifically to own Python-related Intellectual Property.
        Zope Corporation was a sponsoring member of the PSF.
        
        All Python releases are Open Source (see https://opensource.org for
        the Open Source Definition).  Historically, most, but not all, Python
        releases have also been GPL-compatible; the table below summarizes
        the various releases.
        
            Release         Derived     Year        Owner       GPL-
                            from                                compatible? (1)
        
            0.9.0 thru 1.2              1991-1995   CWI         yes
            1.3 thru 1.5.2  1.2         1995-1999   CNRI        yes
            1.6             1.5.2       2000        CNRI        no
            2.0             1.6         2000        BeOpen.com  no
            1.6.1           1.6         2001        CNRI        yes (2)
            2.1             2.0****.1   2001        PSF         no
            2.0.1           2.0****.1   2001        PSF         yes
            2.1.1           2.1+2.0.1   2001        PSF         yes
            2.1.2           2.1.1       2002        PSF         yes
            2.1.3           2.1.2       2002        PSF         yes
            2.2 and above   2.1.1       2001-now    PSF         yes
        
        Footnotes:
        
        (1) GPL-compatible doesn't mean that we're distributing Python under
            the GPL.  All Python licenses, unlike the GPL, let you distribute
            a modified version without making your changes open source.  The
            GPL-compatible licenses make it possible to combine Python with
            other software that is released under the GPL; the others don't.
        
        (2) According to Richard Stallman, 1.6.1 is not GPL-compatible,
            because its license has a choice of law clause.  According to
            CNRI, however, Stallman's lawyer has told CNRI's lawyer that 1.6.1
            is "not incompatible" with the GPL.
        
        Thanks to the many outside volunteers who have worked under Guido's
        direction to make these releases possible.
        
        
        B. TERMS AND CONDITIONS FOR ACCESSING OR OTHERWISE USING PYTHON
        ===============================================================
        
        Python software and documentation are licensed under the
        Python Software Foundation License Version 2.
        
        Starting with Python 3.8.6, examples, recipes, and other code in
        the documentation are dual licensed under the PSF License Version 2
        and the Zero-Clause BSD license.
        
        Some software incorporated into Python is under different licenses.
        The licenses are listed with code falling under that license.
        
        
        PYTHON SOFTWARE FOUNDATION LICENSE VERSION 2
        --------------------------------------------
        
        1. This LICENSE AGREEMENT is between the Python Software Foundation
        ("PSF"), and the Individual or Organization ("Licensee") accessing and
        otherwise using this software ("Python") in source or binary form and
        its associated documentation.
        
        2. Subject to the terms and conditions of this License Agreement, PSF hereby
        grants Licensee a nonexclusive, royalty-free, world-wide license to reproduce,
        analyze, test, perform and/or display publicly, prepare derivative works,
        distribute, and otherwise use Python alone or in any derivative version,
        provided, however, that PSF's License Agreement and PSF's notice of copyright,
        i.e., "Copyright (c) 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010,
        2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023 Python Software Foundation;
        All Rights Reserved" are retained in Python alone or in any derivative version
        prepared by Licensee.
        
        3. In the event Licensee prepares a derivative work that is based on
        or incorporates Python or any part thereof, and wants to make
        the derivative work available to others as provided herein, then
        Licensee hereby agrees to include in any such work a brief summary of
        the changes made to Python.
        
        4. PSF is making Python available to Licensee on an "AS IS"
        basis.  PSF MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
        IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, PSF MAKES NO AND
        DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
        FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF PYTHON WILL NOT
        INFRINGE ANY THIRD PARTY RIGHTS.
        
        5. PSF SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON
        FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS
        A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON,
        OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.
        
        6. This License Agreement will automatically terminate upon a material
        breach of its terms and conditions.
        
        7. Nothing in this License Agreement shall be deemed to create any
        relationship of agency, partnership, or joint venture between PSF and
        Licensee.  This License Agreement does not grant permission to use PSF
        trademarks or trade name in a trademark sense to endorse or promote
        products or services of Licensee, or any third party.
        
        8. By copying, installing or otherwise using Python, Licensee
        agrees to be bound by the terms and conditions of this License
        Agreement.
        
        
        BEOPEN.COM LICENSE AGREEMENT FOR PYTHON 2.0
        -------------------------------------------
        
        BEOPEN PYTHON OPEN SOURCE LICENSE AGREEMENT VERSION 1
        
        1. This LICENSE AGREEMENT is between BeOpen.com ("BeOpen"), having an
        office at 160 Saratoga Avenue, Santa Clara, CA 95051, and the
        Individual or Organization ("Licensee") accessing and otherwise using
        this software in source or binary form and its associated
        documentation ("the Software").
        
        2. Subject to the terms and conditions of this BeOpen Python License
        Agreement, BeOpen hereby grants Licensee a non-exclusive,
        royalty-free, world-wide license to reproduce, analyze, test, perform
        and/or display publicly, prepare derivative works, distribute, and
        otherwise use the Software alone or in any derivative version,
        provided, however, that the BeOpen Python License is retained in the
        Software, alone or in any derivative version prepared by Licensee.
        
        3. BeOpen is making the Software available to Licensee on an "AS IS"
        basis.  BEOPEN MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
        IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, BEOPEN MAKES NO AND
        DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
        FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF THE SOFTWARE WILL NOT
        INFRINGE ANY THIRD PARTY RIGHTS.
        
        4. BEOPEN SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF THE
        SOFTWARE FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS
        AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THE SOFTWARE, OR ANY
        DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.
        
        5. This License Agreement will automatically terminate upon a material
        breach of its terms and conditions.
        
        6. This License Agreement shall be governed by and interpreted in all
        respects by the law of the State of California, excluding conflict of
        law provisions.  Nothing in this License Agreement shall be deemed to
        create any relationship of agency, partnership, or joint venture
        between BeOpen and Licensee.  This License Agreement does not grant
        permission to use BeOpen trademarks or trade names in a trademark
        sense to endorse or promote products or services of Licensee, or any
        third party.  As an exception, the "BeOpen Python" logos available at
        http://www.pythonlabs.com/logos.html may be used according to the
        permissions granted on that web page.
        
        7. By copying, installing or otherwise using the software, Licensee
        agrees to be bound by the terms and conditions of this License
        Agreement.
        
        
        CNRI LICENSE AGREEMENT FOR PYTHON 1.6.1
        ---------------------------------------
        
        1. This LICENSE AGREEMENT is between the Corporation for National
        Research Initiatives, having an office at 1895 Preston White Drive,
        Reston, VA 20191 ("CNRI"), and the Individual or Organization
        ("Licensee") accessing and otherwise using Python 1.6.1 software in
        source or binary form and its associated documentation.
        
        2. Subject to the terms and conditions of this License Agreement, CNRI
        hereby grants Licensee a nonexclusive, royalty-free, world-wide
        license to reproduce, analyze, test, perform and/or display publicly,
        prepare derivative works, distribute, and otherwise use Python 1.6.1
        alone or in any derivative version, provided, however, that CNRI's
        License Agreement and CNRI's notice of copyright, i.e., "Copyright (c)
        1995-2001 Corporation for National Research Initiatives; All Rights
        Reserved" are retained in Python 1.6.1 alone or in any derivative
        version prepared by Licensee.  Alternately, in lieu of CNRI's License
        Agreement, Licensee may substitute the following text (omitting the
        quotes): "Python 1.6.1 is made available subject to the terms and
        conditions in CNRI's License Agreement.  This Agreement together with
        Python 1.6.1 may be located on the internet using the following
        unique, persistent identifier (known as a handle): 1895.22/1013.  This
        Agreement may also be obtained from a proxy server on the internet
        using the following URL: http://hdl.handle.net/1895.22/1013".
        
        3. In the event Licensee prepares a derivative work that is based on
        or incorporates Python 1.6.1 or any part thereof, and wants to make
        the derivative work available to others as provided herein, then
        Licensee hereby agrees to include in any such work a brief summary of
        the changes made to Python 1.6.1.
        
        4. CNRI is making Python 1.6.1 available to Licensee on an "AS IS"
        basis.  CNRI MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
        IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, CNRI MAKES NO AND
        DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
        FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF PYTHON 1.6.1 WILL NOT
        INFRINGE ANY THIRD PARTY RIGHTS.
        
        5. CNRI SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON
        1.6.1 FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS
        A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON 1.6.1,
        OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.
        
        6. This License Agreement will automatically terminate upon a material
        breach of its terms and conditions.
        
        7. This License Agreement shall be governed by the federal
        intellectual property law of the United States, including without
        limitation the federal copyright law, and, to the extent such
        U.S. federal law does not apply, by the law of the Commonwealth of
        Virginia, excluding Virginia's conflict of law provisions.
        Notwithstanding the foregoing, with regard to derivative works based
        on Python 1.6.1 that incorporate non-separable material that was
        previously distributed under the GNU General Public License (GPL), the
        law of the Commonwealth of Virginia shall govern this License
        Agreement only as to issues arising under or with respect to
        Paragraphs 4, 5, and 7 of this License Agreement.  Nothing in this
        License Agreement shall be deemed to create any relationship of
        agency, partnership, or joint venture between CNRI and Licensee.  This
        License Agreement does not grant permission to use CNRI trademarks or
        trade name in a trademark sense to endorse or promote products or
        services of Licensee, or any third party.
        
        8. By clicking on the "ACCEPT" button where indicated, or by copying,
        installing or otherwise using Python 1.6.1, Licensee agrees to be
        bound by the terms and conditions of this License Agreement.
        
                ACCEPT
        
        
        CWI LICENSE AGREEMENT FOR PYTHON 0.9.0 THROUGH 1.2
        --------------------------------------------------
        
        Copyright (c) 1991 - 1995, Stichting Mathematisch Centrum Amsterdam,
        The Netherlands.  All rights reserved.
        
        Permission to use, copy, modify, and distribute this software and its
        documentation for any purpose and without fee is hereby granted,
        provided that the above copyright notice appear in all copies and that
        both that copyright notice and this permission notice appear in
        supporting documentation, and that the name of Stichting Mathematisch
        Centrum or CWI not be used in advertising or publicity pertaining to
        distribution of the software without specific, written prior
        permission.
        
        STICHTING MATHEMATISCH CENTRUM DISCLAIMS ALL WARRANTIES WITH REGARD TO
        THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
        FITNESS, IN NO EVENT SHALL STICHTING MATHEMATISCH CENTRUM BE LIABLE
        FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
        WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
        ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT
        OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
        
        ZERO-CLAUSE BSD LICENSE FOR CODE IN THE PYTHON DOCUMENTATION
        ----------------------------------------------------------------------
        
        Permission to use, copy, modify, and/or distribute this software for any
        purpose with or without fee is hereby granted.
        
        THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
        REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
        AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
        INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
        LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
        OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
        PERFORMANCE OF THIS SOFTWARE.
        Copyright (c) 2014, Al Sweigart
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        * Redistributions of source code must retain the above copyright notice, this
          list of conditions and the following disclaimer.
        
        * Redistributions in binary form must reproduce the above copyright notice,
          this list of conditions and the following disclaimer in the documentation
          and/or other materials provided with the distribution.
        
        * Neither the name of the {organization} nor the names of its
          contributors may be used to endorse or promote products derived from
          this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.Copyright (c) 2017 Anthony Sottile
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in
        all copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
        THE SOFTWARE.Copyright (c) 2015-2019 Jared Hobbs
        
        Permission is hereby granted, free of charge, to any person obtaining a copy of
        this software and associated documentation files (the "Software"), to deal in
        the Software without restriction, including without limitation the rights to
        use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
        of the Software, and to permit persons to whom the Software is furnished to do
        so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.Developed by ESN, an Electronic Arts Inc. studio.
        Copyright (c) 2014, Electronic Arts Inc.
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        * Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
        * Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.
        * Neither the name of ESN, Electronic Arts Inc. nor the
        names of its contributors may be used to endorse or promote products
        derived from this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
        ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
        WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL ELECTRONIC ARTS INC. BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
        (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
        LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
        ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
        (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
        SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
        
        ----
        
        Portions of code from MODP_ASCII - Ascii transformations (upper/lower, etc)
        https://github.com/client9/stringencoders
        
          Copyright 2005, 2006, 2007
          Nick Galbreath -- nickg [at] modp [dot] com
          All rights reserved.
        
          Redistribution and use in source and binary forms, with or without
          modification, are permitted provided that the following conditions are
          met:
        
            Redistributions of source code must retain the above copyright
            notice, this list of conditions and the following disclaimer.
        
            Redistributions in binary form must reproduce the above copyright
            notice, this list of conditions and the following disclaimer in the
            documentation and/or other materials provided with the distribution.
        
            Neither the name of the modp.com nor the names of its
            contributors may be used to endorse or promote products derived from
            this software without specific prior written permission.
        
          THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
          "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
          LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
          A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
          OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
          SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
          LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
          DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
          THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
          (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
          OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
        
          This is the standard "new" BSD license:
          http://www.opensource.org/licenses/bsd-license.php
        
        https://github.com/client9/stringencoders/blob/cfd5c1507325ae497ea9bacdacba12c0ffd79d30/COPYING
        
        ----
        
        Numeric decoder derived from from TCL library
        https://opensource.apple.com/source/tcl/tcl-14/tcl/license.terms
         * Copyright (c) 1988-1993 The Regents of the University of California.
         * Copyright (c) 1994 Sun Microsystems, Inc.
        
          This software is copyrighted by the Regents of the University of
          California, Sun Microsystems, Inc., Scriptics Corporation, ActiveState
          Corporation and other parties.  The following terms apply to all files
          associated with the software unless explicitly disclaimed in
          individual files.
        
          The authors hereby grant permission to use, copy, modify, distribute,
          and license this software and its documentation for any purpose, provided
          that existing copyright notices are retained in all copies and that this
          notice is included verbatim in any distributions. No written agreement,
          license, or royalty fee is required for any of the authorized uses.
          Modifications to this software may be copyrighted by their authors
          and need not follow the licensing terms described here, provided that
          the new terms are clearly indicated on the first page of each file where
          they apply.
        
          IN NO EVENT SHALL THE AUTHORS OR DISTRIBUTORS BE LIABLE TO ANY PARTY
          FOR DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES
          ARISING OUT OF THE USE OF THIS SOFTWARE, ITS DOCUMENTATION, OR ANY
          DERIVATIVES THEREOF, EVEN IF THE AUTHORS HAVE BEEN ADVISED OF THE
          POSSIBILITY OF SUCH DAMAGE.
        
          THE AUTHORS AND DISTRIBUTORS SPECIFICALLY DISCLAIM ANY WARRANTIES,
          INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY,
          FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.  THIS SOFTWARE
          IS PROVIDED ON AN "AS IS" BASIS, AND THE AUTHORS AND DISTRIBUTORS HAVE
          NO OBLIGATION TO PROVIDE MAINTENANCE, SUPPORT, UPDATES, ENHANCEMENTS, OR
          MODIFICATIONS.
        
          GOVERNMENT USE: If you are acquiring this software on behalf of the
          U.S. government, the Government shall have only "Restricted Rights"
          in the software and related documentation as defined in the Federal
          Acquisition Regulations (FARs) in Clause 52.227.19 (c) (2).  If you
          are acquiring the software on behalf of the Department of Defense, the
          software shall be classified as "Commercial Computer Software" and the
          Government shall have only "Restricted Rights" as defined in Clause
          252.227-7013 (c) (1) of DFARs.  Notwithstanding the foregoing, the
          authors grant the U.S. Government and others acting in its behalf
          permission to use and distribute the software in accordance with the
          terms specified in this license.Apache License
        Version 2.0, January 2004
        http://www.apache.org/licenses/
        
        TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION
        
        1. Definitions.
        
        "License" shall mean the terms and conditions for use, reproduction, and
        distribution as defined by Sections 1 through 9 of this document.
        
        "Licensor" shall mean the copyright owner or entity authorized by the copyright
        owner that is granting the License.
        
        "Legal Entity" shall mean the union of the acting entity and all other entities
        that control, are controlled by, or are under common control with that entity.
        For the purposes of this definition, "control" means (i) the power, direct or
        indirect, to cause the direction or management of such entity, whether by
        contract or otherwise, or (ii) ownership of fifty percent (50%) or more of the
        outstanding shares, or (iii) beneficial ownership of such entity.
        
        "You" (or "Your") shall mean an individual or Legal Entity exercising
        permissions granted by this License.
        
        "Source" form shall mean the preferred form for making modifications, including
        but not limited to software source code, documentation source, and configuration
        files.
        
        "Object" form shall mean any form resulting from mechanical transformation or
        translation of a Source form, including but not limited to compiled object code,
        generated documentation, and conversions to other media types.
        
        "Work" shall mean the work of authorship, whether in Source or Object form, made
        available under the License, as indicated by a copyright notice that is included
        in or attached to the work (an example is provided in the Appendix below).
        
        "Derivative Works" shall mean any work, whether in Source or Object form, that
        is based on (or derived from) the Work and for which the editorial revisions,
        annotations, elaborations, or other modifications represent, as a whole, an
        original work of authorship. For the purposes of this License, Derivative Works
        shall not include works that remain separable from, or merely link (or bind by
        name) to the interfaces of, the Work and Derivative Works thereof.
        
        "Contribution" shall mean any work of authorship, including the original version
        of the Work and any modifications or additions to that Work or Derivative Works
        thereof, that is intentionally submitted to Licensor for inclusion in the Work
        by the copyright owner or by an individual or Legal Entity authorized to submit
        on behalf of the copyright owner. For the purposes of this definition,
        "submitted" means any form of electronic, verbal, or written communication sent
        to the Licensor or its representatives, including but not limited to
        communication on electronic mailing lists, source code control systems, and
        issue tracking systems that are managed by, or on behalf of, the Licensor for
        the purpose of discussing and improving the Work, but excluding communication
        that is conspicuously marked or otherwise designated in writing by the copyright
        owner as "Not a Contribution."
        
        "Contributor" shall mean Licensor and any individual or Legal Entity on behalf
        of whom a Contribution has been received by Licensor and subsequently
        incorporated within the Work.
        
        2. Grant of Copyright License.
        
        Subject to the terms and conditions of this License, each Contributor hereby
        grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free,
        irrevocable copyright license to reproduce, prepare Derivative Works of,
        publicly display, publicly perform, sublicense, and distribute the Work and such
        Derivative Works in Source or Object form.
        
        3. Grant of Patent License.
        
        Subject to the terms and conditions of this License, each Contributor hereby
        grants to You a perpetual, worldwide, non-exclusive, no-charge, royalty-free,
        irrevocable (except as stated in this section) patent license to make, have
        made, use, offer to sell, sell, import, and otherwise transfer the Work, where
        such license applies only to those patent claims licensable by such Contributor
        that are necessarily infringed by their Contribution(s) alone or by combination
        of their Contribution(s) with the Work to which such Contribution(s) was
        submitted. If You institute patent litigation against any entity (including a
        cross-claim or counterclaim in a lawsuit) alleging that the Work or a
        Contribution incorporated within the Work constitutes direct or contributory
        patent infringement, then any patent licenses granted to You under this License
        for that Work shall terminate as of the date such litigation is filed.
        
        4. Redistribution.
        
        You may reproduce and distribute copies of the Work or Derivative Works thereof
        in any medium, with or without modifications, and in Source or Object form,
        provided that You meet the following conditions:
        
        You must give any other recipients of the Work or Derivative Works a copy of
        this License; and
        You must cause any modified files to carry prominent notices stating that You
        changed the files; and
        You must retain, in the Source form of any Derivative Works that You distribute,
        all copyright, patent, trademark, and attribution notices from the Source form
        of the Work, excluding those notices that do not pertain to any part of the
        Derivative Works; and
        If the Work includes a "NOTICE" text file as part of its distribution, then any
        Derivative Works that You distribute must include a readable copy of the
        attribution notices contained within such NOTICE file, excluding those notices
        that do not pertain to any part of the Derivative Works, in at least one of the
        following places: within a NOTICE text file distributed as part of the
        Derivative Works; within the Source form or documentation, if provided along
        with the Derivative Works; or, within a display generated by the Derivative
        Works, if and wherever such third-party notices normally appear. The contents of
        the NOTICE file are for informational purposes only and do not modify the
        License. You may add Your own attribution notices within Derivative Works that
        You distribute, alongside or as an addendum to the NOTICE text from the Work,
        provided that such additional attribution notices cannot be construed as
        modifying the License.
        You may add Your own copyright statement to Your modifications and may provide
        additional or different license terms and conditions for use, reproduction, or
        distribution of Your modifications, or for any such Derivative Works as a whole,
        provided Your use, reproduction, and distribution of the Work otherwise complies
        with the conditions stated in this License.
        
        5. Submission of Contributions.
        
        Unless You explicitly state otherwise, any Contribution intentionally submitted
        for inclusion in the Work by You to the Licensor shall be under the terms and
        conditions of this License, without any additional terms or conditions.
        Notwithstanding the above, nothing herein shall supersede or modify the terms of
        any separate license agreement you may have executed with Licensor regarding
        such Contributions.
        
        6. Trademarks.
        
        This License does not grant permission to use the trade names, trademarks,
        service marks, or product names of the Licensor, except as required for
        reasonable and customary use in describing the origin of the Work and
        reproducing the content of the NOTICE file.
        
        7. Disclaimer of Warranty.
        
        Unless required by applicable law or agreed to in writing, Licensor provides the
        Work (and each Contributor provides its Contributions) on an "AS IS" BASIS,
        WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied,
        including, without limitation, any warranties or conditions of TITLE,
        NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A PARTICULAR PURPOSE. You are
        solely responsible for determining the appropriateness of using or
        redistributing the Work and assume any risks associated with Your exercise of
        permissions under this License.
        
        8. Limitation of Liability.
        
        In no event and under no legal theory, whether in tort (including negligence),
        contract, or otherwise, unless required by applicable law (such as deliberate
        and grossly negligent acts) or agreed to in writing, shall any Contributor be
        liable to You for damages, including any direct, indirect, special, incidental,
        or consequential damages of any character arising as a result of this License or
        out of the use or inability to use the Work (including but not limited to
        damages for loss of goodwill, work stoppage, computer failure or malfunction, or
        any and all other commercial damages or losses), even if such Contributor has
        been advised of the possibility of such damages.
        
        9. Accepting Warranty or Additional Liability.
        
        While redistributing the Work or Derivative Works thereof, You may choose to
        offer, and charge a fee for, acceptance of support, warranty, indemnity, or
        other liability obligations and/or rights consistent with this License. However,
        in accepting such obligations, You may act only on Your own behalf and on Your
        sole responsibility, not on behalf of any other Contributor, and only if You
        agree to indemnify, defend, and hold each Contributor harmless for any liability
        incurred by, or claims asserted against, such Contributor by reason of your
        accepting any such warranty or additional liability.
        
        END OF TERMS AND CONDITIONS
        
        APPENDIX: How to apply the Apache License to your work
        
        To apply the Apache License to your work, attach the following boilerplate
        notice, with the fields enclosed by brackets "[]" replaced with your own
        identifying information. (Don't include the brackets!) The text should be
        enclosed in the appropriate comment syntax for the file format. We also
        recommend that a file or class name and description of purpose be included on
        the same "printed page" as the copyright notice for easier identification within
        third-party archives.
        
           Copyright [yyyy] [name of copyright owner]
        
           Licensed under the Apache License, Version 2.0 (the "License");
           you may not use this file except in compliance with the License.
           You may obtain a copy of the License at
        
             http://www.apache.org/licenses/LICENSE-2.0
        
           Unless required by applicable law or agreed to in writing, software
           distributed under the License is distributed on an "AS IS" BASIS,
           WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
           See the License for the specific language governing permissions and
           limitations under the License.
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Cython
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Scientific/Engineering
Project-URL: Homepage, https://pandas.pydata.org
Project-URL: Documentation, https://pandas.pydata.org/docs/
Project-URL: Repository, https://github.com/pandas-dev/pandas
Requires-Python: >=3.9
Requires-Dist: numpy>=1.22.4; python_version < "3.11"
Requires-Dist: numpy>=1.23.2; python_version == "3.11"
Requires-Dist: numpy>=1.26.0; python_version >= "3.12"
Requires-Dist: python-dateutil>=2.8.2
Requires-Dist: pytz>=2020.1
Requires-Dist: tzdata>=2022.7
Requires-Dist: hypothesis>=6.46.1; extra == "test"
Requires-Dist: pytest>=7.3.2; extra == "test"
Requires-Dist: pytest-xdist>=2.2.0; extra == "test"
Requires-Dist: pyarrow>=10.0.1; extra == "pyarrow"
Requires-Dist: bottleneck>=1.3.6; extra == "performance"
Requires-Dist: numba>=0.56.4; extra == "performance"
Requires-Dist: numexpr>=2.8.4; extra == "performance"
Requires-Dist: scipy>=1.10.0; extra == "computation"
Requires-Dist: xarray>=2022.12.0; extra == "computation"
Requires-Dist: fsspec>=2022.11.0; extra == "fss"
Requires-Dist: s3fs>=2022.11.0; extra == "aws"
Requires-Dist: gcsfs>=2022.11.0; extra == "gcp"
Requires-Dist: pandas-gbq>=0.19.0; extra == "gcp"
Requires-Dist: odfpy>=1.4.1; extra == "excel"
Requires-Dist: openpyxl>=3.1.0; extra == "excel"
Requires-Dist: python-calamine>=0.1.7; extra == "excel"
Requires-Dist: pyxlsb>=1.0.10; extra == "excel"
Requires-Dist: xlrd>=2.0.1; extra == "excel"
Requires-Dist: xlsxwriter>=3.0.5; extra == "excel"
Requires-Dist: pyarrow>=10.0.1; extra == "parquet"
Requires-Dist: pyarrow>=10.0.1; extra == "feather"
Requires-Dist: tables>=3.8.0; extra == "hdf5"
Requires-Dist: pyreadstat>=1.2.0; extra == "spss"
Requires-Dist: SQLAlchemy>=2.0.0; extra == "postgresql"
Requires-Dist: psycopg2>=2.9.6; extra == "postgresql"
Requires-Dist: adbc-driver-postgresql>=0.8.0; extra == "postgresql"
Requires-Dist: SQLAlchemy>=2.0.0; extra == "mysql"
Requires-Dist: pymysql>=1.0.2; extra == "mysql"
Requires-Dist: SQLAlchemy>=2.0.0; extra == "sql-other"
Requires-Dist: adbc-driver-postgresql>=0.8.0; extra == "sql-other"
Requires-Dist: adbc-driver-sqlite>=0.8.0; extra == "sql-other"
Requires-Dist: beautifulsoup4>=4.11.2; extra == "html"
Requires-Dist: html5lib>=1.1; extra == "html"
Requires-Dist: lxml>=4.9.2; extra == "html"
Requires-Dist: lxml>=4.9.2; extra == "xml"
Requires-Dist: matplotlib>=3.6.3; extra == "plot"
Requires-Dist: jinja2>=3.1.2; extra == "output-formatting"
Requires-Dist: tabulate>=0.9.0; extra == "output-formatting"
Requires-Dist: PyQt5>=5.15.9; extra == "clipboard"
Requires-Dist: qtpy>=2.3.0; extra == "clipboard"
Requires-Dist: zstandard>=0.19.0; extra == "compression"
Requires-Dist: dataframe-api-compat>=0.1.7; extra == "consortium-standard"
Requires-Dist: adbc-driver-postgresql>=0.8.0; extra == "all"
Requires-Dist: adbc-driver-sqlite>=0.8.0; extra == "all"
Requires-Dist: beautifulsoup4>=4.11.2; extra == "all"
Requires-Dist: bottleneck>=1.3.6; extra == "all"
Requires-Dist: dataframe-api-compat>=0.1.7; extra == "all"
Requires-Dist: fastparquet>=2022.12.0; extra == "all"
Requires-Dist: fsspec>=2022.11.0; extra == "all"
Requires-Dist: gcsfs>=2022.11.0; extra == "all"
Requires-Dist: html5lib>=1.1; extra == "all"
Requires-Dist: hypothesis>=6.46.1; extra == "all"
Requires-Dist: jinja2>=3.1.2; extra == "all"
Requires-Dist: lxml>=4.9.2; extra == "all"
Requires-Dist: matplotlib>=3.6.3; extra == "all"
Requires-Dist: numba>=0.56.4; extra == "all"
Requires-Dist: numexpr>=2.8.4; extra == "all"
Requires-Dist: odfpy>=1.4.1; extra == "all"
Requires-Dist: openpyxl>=3.1.0; extra == "all"
Requires-Dist: pandas-gbq>=0.19.0; extra == "all"
Requires-Dist: psycopg2>=2.9.6; extra == "all"
Requires-Dist: pyarrow>=10.0.1; extra == "all"
Requires-Dist: pymysql>=1.0.2; extra == "all"
Requires-Dist: PyQt5>=5.15.9; extra == "all"
Requires-Dist: pyreadstat>=1.2.0; extra == "all"
Requires-Dist: pytest>=7.3.2; extra == "all"
Requires-Dist: pytest-xdist>=2.2.0; extra == "all"
Requires-Dist: python-calamine>=0.1.7; extra == "all"
Requires-Dist: pyxlsb>=1.0.10; extra == "all"
Requires-Dist: qtpy>=2.3.0; extra == "all"
Requires-Dist: scipy>=1.10.0; extra == "all"
Requires-Dist: s3fs>=2022.11.0; extra == "all"
Requires-Dist: SQLAlchemy>=2.0.0; extra == "all"
Requires-Dist: tables>=3.8.0; extra == "all"
Requires-Dist: tabulate>=0.9.0; extra == "all"
Requires-Dist: xarray>=2022.12.0; extra == "all"
Requires-Dist: xlrd>=2.0.1; extra == "all"
Requires-Dist: xlsxwriter>=3.0.5; extra == "all"
Requires-Dist: zstandard>=0.19.0; extra == "all"
Provides-Extra: test
Provides-Extra: pyarrow
Provides-Extra: performance
Provides-Extra: computation
Provides-Extra: fss
Provides-Extra: aws
Provides-Extra: gcp
Provides-Extra: excel
Provides-Extra: parquet
Provides-Extra: feather
Provides-Extra: hdf5
Provides-Extra: spss
Provides-Extra: postgresql
Provides-Extra: mysql
Provides-Extra: sql-other
Provides-Extra: html
Provides-Extra: xml
Provides-Extra: plot
Provides-Extra: output-formatting
Provides-Extra: clipboard
Provides-Extra: compression
Provides-Extra: consortium-standard
Provides-Extra: all
Description-Content-Type: text/markdown

<div align="center">
  <img src="https://pandas.pydata.org/static/img/pandas.svg"><br>
</div>

-----------------

# pandas: powerful Python data analysis toolkit

| | |
| --- | --- |
| Testing | [![CI - Test](https://github.com/pandas-dev/pandas/actions/workflows/unit-tests.yml/badge.svg)](https://github.com/pandas-dev/pandas/actions/workflows/unit-tests.yml) [![Coverage](https://codecov.io/github/pandas-dev/pandas/coverage.svg?branch=main)](https://codecov.io/gh/pandas-dev/pandas) |
| Package | [![PyPI Latest Release](https://img.shields.io/pypi/v/pandas.svg)](https://pypi.org/project/pandas/) [![PyPI Downloads](https://img.shields.io/pypi/dm/pandas.svg?label=PyPI%20downloads)](https://pypi.org/project/pandas/) [![Conda Latest Release](https://anaconda.org/conda-forge/pandas/badges/version.svg)](https://anaconda.org/conda-forge/pandas) [![Conda Downloads](https://img.shields.io/conda/dn/conda-forge/pandas.svg?label=Conda%20downloads)](https://anaconda.org/conda-forge/pandas) |
| Meta | [![Powered by NumFOCUS](https://img.shields.io/badge/powered%20by-NumFOCUS-orange.svg?style=flat&colorA=E1523D&colorB=007D8A)](https://numfocus.org) [![DOI](https://zenodo.org/badge/DOI/10.5281/zenodo.3509134.svg)](https://doi.org/10.5281/zenodo.3509134) [![License - BSD 3-Clause](https://img.shields.io/pypi/l/pandas.svg)](https://github.com/pandas-dev/pandas/blob/main/LICENSE) [![Slack](https://img.shields.io/badge/join_Slack-information-brightgreen.svg?logo=slack)](https://pandas.pydata.org/docs/dev/development/community.html?highlight=slack#community-slack) |


## What is it?

**pandas** is a Python package that provides fast, flexible, and expressive data
structures designed to make working with "relational" or "labeled" data both
easy and intuitive. It aims to be the fundamental high-level building block for
doing practical, **real world** data analysis in Python. Additionally, it has
the broader goal of becoming **the most powerful and flexible open source data
analysis / manipulation tool available in any language**. It is already well on
its way towards this goal.

## Table of Contents

- [Main Features](#main-features)
- [Where to get it](#where-to-get-it)
- [Dependencies](#dependencies)
- [Installation from sources](#installation-from-sources)
- [License](#license)
- [Documentation](#documentation)
- [Background](#background)
- [Getting Help](#getting-help)
- [Discussion and Development](#discussion-and-development)
- [Contributing to pandas](#contributing-to-pandas)

## Main Features
Here are just a few of the things that pandas does well:

  - Easy handling of [**missing data**][missing-data] (represented as
    `NaN`, `NA`, or `NaT`) in floating point as well as non-floating point data
  - Size mutability: columns can be [**inserted and
    deleted**][insertion-deletion] from DataFrame and higher dimensional
    objects
  - Automatic and explicit [**data alignment**][alignment]: objects can
    be explicitly aligned to a set of labels, or the user can simply
    ignore the labels and let `Series`, `DataFrame`, etc. automatically
    align the data for you in computations
  - Powerful, flexible [**group by**][groupby] functionality to perform
    split-apply-combine operations on data sets, for both aggregating
    and transforming data
  - Make it [**easy to convert**][conversion] ragged,
    differently-indexed data in other Python and NumPy data structures
    into DataFrame objects
  - Intelligent label-based [**slicing**][slicing], [**fancy
    indexing**][fancy-indexing], and [**subsetting**][subsetting] of
    large data sets
  - Intuitive [**merging**][merging] and [**joining**][joining] data
    sets
  - Flexible [**reshaping**][reshape] and [**pivoting**][pivot-table] of
    data sets
  - [**Hierarchical**][mi] labeling of axes (possible to have multiple
    labels per tick)
  - Robust IO tools for loading data from [**flat files**][flat-files]
    (CSV and delimited), [**Excel files**][excel], [**databases**][db],
    and saving/loading data from the ultrafast [**HDF5 format**][hdfstore]
  - [**Time series**][timeseries]-specific functionality: date range
    generation and frequency conversion, moving window statistics,
    date shifting and lagging


   [missing-data]: https://pandas.pydata.org/pandas-docs/stable/user_guide/missing_data.html
   [insertion-deletion]: https://pandas.pydata.org/pandas-docs/stable/user_guide/dsintro.html#column-selection-addition-deletion
   [alignment]: https://pandas.pydata.org/pandas-docs/stable/user_guide/dsintro.html?highlight=alignment#intro-to-data-structures
   [groupby]: https://pandas.pydata.org/pandas-docs/stable/user_guide/groupby.html#group-by-split-apply-combine
   [conversion]: https://pandas.pydata.org/pandas-docs/stable/user_guide/dsintro.html#dataframe
   [slicing]: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#slicing-ranges
   [fancy-indexing]: https://pandas.pydata.org/pandas-docs/stable/user_guide/advanced.html#advanced
   [subsetting]: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#boolean-indexing
   [merging]: https://pandas.pydata.org/pandas-docs/stable/user_guide/merging.html#database-style-dataframe-or-named-series-joining-merging
   [joining]: https://pandas.pydata.org/pandas-docs/stable/user_guide/merging.html#joining-on-index
   [reshape]: https://pandas.pydata.org/pandas-docs/stable/user_guide/reshaping.html
   [pivot-table]: https://pandas.pydata.org/pandas-docs/stable/user_guide/reshaping.html
   [mi]: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#hierarchical-indexing-multiindex
   [flat-files]: https://pandas.pydata.org/pandas-docs/stable/user_guide/io.html#csv-text-files
   [excel]: https://pandas.pydata.org/pandas-docs/stable/user_guide/io.html#excel-files
   [db]: https://pandas.pydata.org/pandas-docs/stable/user_guide/io.html#sql-queries
   [hdfstore]: https://pandas.pydata.org/pandas-docs/stable/user_guide/io.html#hdf5-pytables
   [timeseries]: https://pandas.pydata.org/pandas-docs/stable/user_guide/timeseries.html#time-series-date-functionality

## Where to get it
The source code is currently hosted on GitHub at:
https://github.com/pandas-dev/pandas

Binary installers for the latest released version are available at the [Python
Package Index (PyPI)](https://pypi.org/project/pandas) and on [Conda](https://docs.conda.io/en/latest/).

```sh
# conda
conda install -c conda-forge pandas
```

```sh
# or PyPI
pip install pandas
```

The list of changes to pandas between each release can be found
[here](https://pandas.pydata.org/pandas-docs/stable/whatsnew/index.html). For full
details, see the commit logs at https://github.com/pandas-dev/pandas.

## Dependencies
- [NumPy - Adds support for large, multi-dimensional arrays, matrices and high-level mathematical functions to operate on these arrays](https://www.numpy.org)
- [python-dateutil - Provides powerful extensions to the standard datetime module](https://dateutil.readthedocs.io/en/stable/index.html)
- [pytz - Brings the Olson tz database into Python which allows accurate and cross platform timezone calculations](https://github.com/stub42/pytz)

See the [full installation instructions](https://pandas.pydata.org/pandas-docs/stable/install.html#dependencies) for minimum supported versions of required, recommended and optional dependencies.

## Installation from sources
To install pandas from source you need [Cython](https://cython.org/) in addition to the normal
dependencies above. Cython can be installed from PyPI:

```sh
pip install cython
```

In the `pandas` directory (same one where you found this file after
cloning the git repo), execute:

```sh
pip install .
```

or for installing in [development mode](https://pip.pypa.io/en/latest/cli/pip_install/#install-editable):


```sh
python -m pip install -ve . --no-build-isolation --config-settings=editable-verbose=true
```

See the full instructions for [installing from source](https://pandas.pydata.org/docs/dev/development/contributing_environment.html).

## License
[BSD 3](LICENSE)

## Documentation
The official documentation is hosted on [PyData.org](https://pandas.pydata.org/pandas-docs/stable/).

## Background
Work on ``pandas`` started at [AQR](https://www.aqr.com/) (a quantitative hedge fund) in 2008 and
has been under active development since then.

## Getting Help

For usage questions, the best place to go to is [StackOverflow](https://stackoverflow.com/questions/tagged/pandas).
Further, general questions and discussions can also take place on the [pydata mailing list](https://groups.google.com/forum/?fromgroups#!forum/pydata).

## Discussion and Development
Most development discussions take place on GitHub in this repo, via the [GitHub issue tracker](https://github.com/pandas-dev/pandas/issues).

Further, the [pandas-dev mailing list](https://mail.python.org/mailman/listinfo/pandas-dev) can also be used for specialized discussions or design issues, and a [Slack channel](https://pandas.pydata.org/docs/dev/development/community.html?highlight=slack#community-slack) is available for quick development related questions.

There are also frequent [community meetings](https://pandas.pydata.org/docs/dev/development/community.html#community-meeting) for project maintainers open to the community as well as monthly [new contributor meetings](https://pandas.pydata.org/docs/dev/development/community.html#new-contributor-meeting) to help support new contributors.

Additional information on the communication channels can be found on the [contributor community](https://pandas.pydata.org/docs/development/community.html) page.

## Contributing to pandas

[![Open Source Helpers](https://www.codetriage.com/pandas-dev/pandas/badges/users.svg)](https://www.codetriage.com/pandas-dev/pandas)

All contributions, bug reports, bug fixes, documentation improvements, enhancements, and ideas are welcome.

A detailed overview on how to contribute can be found in the **[contributing guide](https://pandas.pydata.org/docs/dev/development/contributing.html)**.

If you are simply looking to start working with the pandas codebase, navigate to the [GitHub "issues" tab](https://github.com/pandas-dev/pandas/issues) and start looking through interesting issues. There are a number of issues listed under [Docs](https://github.com/pandas-dev/pandas/issues?labels=Docs&sort=updated&state=open) and [good first issue](https://github.com/pandas-dev/pandas/issues?labels=good+first+issue&sort=updated&state=open) where you could start out.

You can also triage issues which may include reproducing bug reports, or asking for vital information such as version numbers or reproduction instructions. If you would like to start triaging issues, one easy way to get started is to [subscribe to pandas on CodeTriage](https://www.codetriage.com/pandas-dev/pandas).

Or maybe through using pandas you have an idea of your own or are looking for something in the documentation and thinking ‘this can be improved’...you can do something about it!

Feel free to ask questions on the [mailing list](https://groups.google.com/forum/?fromgroups#!forum/pydata) or on [Slack](https://pandas.pydata.org/docs/dev/development/community.html?highlight=slack#community-slack).

As contributors and maintainers to this project, you are expected to abide by pandas' code of conduct. More information can be found at: [Contributor Code of Conduct](https://github.com/pandas-dev/.github/blob/master/CODE_OF_CONDUCT.md)

<hr>

[Go to Top](#table-of-contents)
