#include <Python.h>

PyMODINIT_FUNC
PyInit_{modname}(void)
{{
    PyObject *tmp;
    if (!(tmp = PyImport_ImportModule("{libname}"))) return NULL;
    PyObject *capsule = PyObject_GetAttrString(tmp, "init_{full_modname}");
    Py_DECREF(tmp);
    if (capsule == NULL) return NULL;
    void *init_func = PyCapsule_GetPointer(capsule, "{libname}.init_{full_modname}");
    Py_DECREF(capsule);
    if (!init_func) {{
        return NULL;
    }}
    return ((PyObject *(*)(void))init_func)();
}}

// distutils sometimes spuriously tells cl to export CPyInit___init__,
// so provide that so it chills out
PyMODINIT_FUNC PyInit___init__(void) {{ return PyInit_{modname}(); }}
